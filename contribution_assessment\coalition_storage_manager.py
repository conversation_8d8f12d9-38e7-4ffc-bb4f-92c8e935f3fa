"""
联盟存储管理器 (Coalition Storage Manager)

本模块实现联盟特定的数据存储结构，为每个联盟创建以代理名称命名的文件夹，
存储实验过程、夏普率、收益率等数据。

主要功能：
1. 为每个联盟创建专用存储文件夹
2. 存储联盟实验过程和结果数据
3. 管理联盟性能指标和历史记录
4. 提供联盟数据查询和分析功能

存储结构：
data/
├── shapley_experiments/
│   ├── experiment_{timestamp}/
│   │   ├── coalition_{NAA_TAA_TRA}/
│   │   │   ├── coalition_info.json
│   │   │   ├── trading_simulation.json
│   │   │   ├── performance_metrics.json
│   │   │   ├── daily_returns.json
│   │   │   └── agent_logs/
│   │   │       ├── NAA/
│   │   │       ├── TAA/
│   │   │       └── TRA/
│   │   └── experiment_summary.json
│   └── latest_experiment -> experiment_{latest_timestamp}
"""

import json
import logging
import os
import shutil
from typing import Dict, List, Any, Optional, Set, Union
from datetime import datetime
from pathlib import Path
import hashlib

class CoalitionStorageManager:
    """
    联盟存储管理器
    
    负责管理联盟特定的数据存储，包括：
    - 联盟文件夹创建和管理
    - 实验数据存储和检索
    - 性能指标记录和分析
    - 历史数据维护
    """
    
    def __init__(self, 
                 base_data_dir: str = "data",
                 logger: Optional[logging.Logger] = None):
        """
        初始化联盟存储管理器
        
        参数:
            base_data_dir: 数据存储基础目录
            logger: 日志记录器
        """
        self.base_data_dir = Path(base_data_dir)
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建主要目录结构
        self.shapley_experiments_dir = self.base_data_dir / "shapley_experiments"
        self.coalition_archives_dir = self.base_data_dir / "coalition_archives"
        self.performance_summaries_dir = self.base_data_dir / "performance_summaries"
        
        for dir_path in [self.shapley_experiments_dir, self.coalition_archives_dir, self.performance_summaries_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.current_experiment_id = None
        self.current_experiment_dir = None
        
        self.logger.info(f"联盟存储管理器初始化完成，数据目录: {self.base_data_dir}")
    
    def start_new_experiment(self, 
                           experiment_config: Dict[str, Any],
                           experiment_id: Optional[str] = None) -> str:
        """
        开始新的Shapley实验
        
        参数:
            experiment_config: 实验配置信息
            experiment_id: 可选的实验ID，如果为None则自动生成
            
        返回:
            实验ID
        """
        if experiment_id is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_id = f"experiment_{timestamp}"
        
        self.current_experiment_id = experiment_id
        self.current_experiment_dir = self.shapley_experiments_dir / experiment_id
        
        # 创建实验目录
        self.current_experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存实验配置
        experiment_info = {
            "experiment_id": experiment_id,
            "start_time": datetime.now().isoformat(),
            "config": experiment_config,
            "status": "started",
            "coalitions": {},
            "summary": {}
        }
        
        experiment_info_file = self.current_experiment_dir / "experiment_info.json"
        with open(experiment_info_file, 'w', encoding='utf-8') as f:
            json.dump(experiment_info, f, ensure_ascii=False, indent=2)
        
        # 创建或更新最新实验链接
        latest_link = self.shapley_experiments_dir / "latest_experiment"
        if latest_link.exists() or latest_link.is_symlink():
            latest_link.unlink()
        
        try:
            latest_link.symlink_to(experiment_id)
        except OSError:
            # Windows可能不支持符号链接，创建文本文件记录
            with open(latest_link, 'w') as f:
                f.write(experiment_id)
        
        self.logger.info(f"开始新实验: {experiment_id}")
        return experiment_id
    
    def create_coalition_storage(self, 
                               coalition: Union[Set[str], List[str]],
                               coalition_info: Dict[str, Any]) -> Path:
        """
        为联盟创建存储文件夹
        
        参数:
            coalition: 联盟成员列表
            coalition_info: 联盟基本信息
            
        返回:
            联盟存储目录路径
        """
        if self.current_experiment_dir is None:
            raise ValueError("未开始实验，请先调用start_new_experiment()")
        
        # 生成联盟文件夹名称
        coalition_set = set(coalition) if isinstance(coalition, list) else coalition
        coalition_name = self._generate_coalition_name(coalition_set)
        
        # 创建联盟目录
        coalition_dir = self.current_experiment_dir / f"coalition_{coalition_name}"
        coalition_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录结构
        subdirs = ["agent_logs", "daily_data", "performance_analysis"]
        for subdir in subdirs:
            (coalition_dir / subdir).mkdir(exist_ok=True)
        
        # 为每个代理创建日志目录
        agent_logs_dir = coalition_dir / "agent_logs"
        for agent_id in coalition_set:
            (agent_logs_dir / agent_id).mkdir(exist_ok=True)
        
        # 保存联盟基本信息
        coalition_info_data = {
            "coalition_name": coalition_name,
            "coalition_members": sorted(list(coalition_set)),
            "coalition_size": len(coalition_set),
            "creation_time": datetime.now().isoformat(),
            "experiment_id": self.current_experiment_id,
            **coalition_info
        }
        
        coalition_info_file = coalition_dir / "coalition_info.json"
        with open(coalition_info_file, 'w', encoding='utf-8') as f:
            json.dump(coalition_info_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"创建联盟存储: {coalition_name}")
        return coalition_dir
    
    def save_coalition_simulation_result(self,
                                       coalition: Union[Set[str], List[str]],
                                       simulation_result: Dict[str, Any],
                                       agent_logs: Optional[Dict[str, Any]] = None) -> None:
        """
        保存联盟交易模拟结果
        
        参数:
            coalition: 联盟成员
            simulation_result: 模拟结果数据
            agent_logs: 代理日志数据
        """
        coalition_set = set(coalition) if isinstance(coalition, list) else coalition
        coalition_name = self._generate_coalition_name(coalition_set)
        coalition_dir = self.current_experiment_dir / f"coalition_{coalition_name}"
        
        if not coalition_dir.exists():
            self.logger.warning(f"联盟目录不存在，创建: {coalition_name}")
            self.create_coalition_storage(coalition_set, {})
        
        # 保存交易模拟结果
        simulation_file = coalition_dir / "trading_simulation.json"
        with open(simulation_file, 'w', encoding='utf-8') as f:
            json.dump(simulation_result, f, ensure_ascii=False, indent=2)
        
        # 提取并保存性能指标
        performance_metrics = self._extract_performance_metrics(simulation_result)
        performance_file = coalition_dir / "performance_metrics.json"
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_metrics, f, ensure_ascii=False, indent=2)
        
        # 保存每日收益数据
        if "daily_returns" in simulation_result:
            daily_returns_file = coalition_dir / "daily_data" / "daily_returns.json"
            daily_returns_data = {
                "coalition": coalition_name,
                "daily_returns": simulation_result["daily_returns"],
                "total_days": len(simulation_result["daily_returns"]),
                "timestamp": datetime.now().isoformat()
            }
            with open(daily_returns_file, 'w', encoding='utf-8') as f:
                json.dump(daily_returns_data, f, ensure_ascii=False, indent=2)
        
        # 保存代理日志
        if agent_logs:
            self._save_agent_logs(coalition_dir, agent_logs)
        
        self.logger.info(f"保存联盟模拟结果: {coalition_name}")
    
    def save_shapley_calculation_result(self,
                                      shapley_values: Dict[str, float],
                                      coalition_values: Dict[frozenset, float],
                                      calculation_metadata: Dict[str, Any]) -> None:
        """
        保存Shapley值计算结果
        
        参数:
            shapley_values: Shapley值结果
            coalition_values: 联盟特征函数值
            calculation_metadata: 计算元数据
        """
        if self.current_experiment_dir is None:
            raise ValueError("未开始实验")
        
        # 保存Shapley值结果
        shapley_result = {
            "experiment_id": self.current_experiment_id,
            "calculation_time": datetime.now().isoformat(),
            "shapley_values": shapley_values,
            "coalition_values": {self._coalition_to_string(k): v for k, v in coalition_values.items()},
            "metadata": calculation_metadata,
            "summary": {
                "total_agents": len(shapley_values),
                "total_coalitions": len(coalition_values),
                "average_shapley": sum(shapley_values.values()) / len(shapley_values) if shapley_values else 0,
                "max_shapley": max(shapley_values.values()) if shapley_values else 0,
                "min_shapley": min(shapley_values.values()) if shapley_values else 0
            }
        }
        
        shapley_file = self.current_experiment_dir / "shapley_results.json"
        with open(shapley_file, 'w', encoding='utf-8') as f:
            json.dump(shapley_result, f, ensure_ascii=False, indent=2)
        
        # 更新实验信息
        self._update_experiment_info({"shapley_values": shapley_values})
        
        self.logger.info(f"保存Shapley计算结果，涉及{len(coalition_values)}个联盟")
    
    def finish_experiment(self, experiment_summary: Dict[str, Any]) -> None:
        """
        完成实验并保存总结
        
        参数:
            experiment_summary: 实验总结信息
        """
        if self.current_experiment_dir is None:
            raise ValueError("未开始实验")
        
        # 更新实验信息
        experiment_summary_data = {
            "experiment_id": self.current_experiment_id,
            "end_time": datetime.now().isoformat(),
            "status": "completed",
            "summary": experiment_summary
        }
        
        self._update_experiment_info(experiment_summary_data)
        
        # 生成实验报告
        self._generate_experiment_report()
        
        self.logger.info(f"实验完成: {self.current_experiment_id}")
        
        # 重置当前实验
        self.current_experiment_id = None
        self.current_experiment_dir = None
    
    def _generate_coalition_name(self, coalition: Set[str]) -> str:
        """生成联盟名称"""
        # 按字母顺序排序以确保一致性
        sorted_agents = sorted(list(coalition))
        return "_".join(sorted_agents)
    
    def _extract_performance_metrics(self, simulation_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取性能指标"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "sharpe_ratio": simulation_result.get("sharpe_ratio", 0.0),
            "total_return": 0.0,
            "volatility": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0,
            "total_trades": 0
        }
        
        # 计算额外指标
        daily_returns = simulation_result.get("daily_returns", [])
        if daily_returns:
            import numpy as np
            returns_array = np.array(daily_returns)
            
            metrics["total_return"] = float(np.sum(returns_array))
            metrics["volatility"] = float(np.std(returns_array))
            
            # 计算最大回撤
            cumulative_returns = np.cumsum(returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = running_max - cumulative_returns
            metrics["max_drawdown"] = float(np.max(drawdown))
            
            # 计算胜率
            positive_days = np.sum(returns_array > 0)
            metrics["win_rate"] = float(positive_days / len(returns_array))
        
        return metrics

    def _save_agent_logs(self, coalition_dir: Path, agent_logs: Dict[str, Any]) -> None:
        """保存代理日志"""
        agent_logs_dir = coalition_dir / "agent_logs"

        for agent_id, logs in agent_logs.items():
            agent_dir = agent_logs_dir / agent_id
            agent_dir.mkdir(exist_ok=True)

            # 保存输入日志
            if "inputs" in logs:
                inputs_file = agent_dir / "inputs.json"
                with open(inputs_file, 'w', encoding='utf-8') as f:
                    json.dump(logs["inputs"], f, ensure_ascii=False, indent=2)

            # 保存输出日志
            if "outputs" in logs:
                outputs_file = agent_dir / "outputs.json"
                with open(outputs_file, 'w', encoding='utf-8') as f:
                    json.dump(logs["outputs"], f, ensure_ascii=False, indent=2)

            # 保存提示词日志
            if "prompts" in logs:
                prompts_file = agent_dir / "prompts.json"
                with open(prompts_file, 'w', encoding='utf-8') as f:
                    json.dump(logs["prompts"], f, ensure_ascii=False, indent=2)

    def _coalition_to_string(self, coalition: frozenset) -> str:
        """将联盟frozenset转换为字符串"""
        return "_".join(sorted(list(coalition)))

    def _update_experiment_info(self, update_data: Dict[str, Any]) -> None:
        """更新实验信息"""
        if self.current_experiment_dir is None:
            return

        experiment_info_file = self.current_experiment_dir / "experiment_info.json"

        if experiment_info_file.exists():
            with open(experiment_info_file, 'r', encoding='utf-8') as f:
                experiment_info = json.load(f)
        else:
            experiment_info = {}

        experiment_info.update(update_data)

        with open(experiment_info_file, 'w', encoding='utf-8') as f:
            json.dump(experiment_info, f, ensure_ascii=False, indent=2)

    def _generate_experiment_report(self) -> None:
        """生成实验报告"""
        if self.current_experiment_dir is None:
            return

        report_data = {
            "experiment_id": self.current_experiment_id,
            "report_generation_time": datetime.now().isoformat(),
            "coalition_summary": {},
            "performance_analysis": {},
            "recommendations": []
        }

        # 收集联盟数据
        coalition_dirs = [d for d in self.current_experiment_dir.iterdir()
                         if d.is_dir() and d.name.startswith("coalition_")]

        for coalition_dir in coalition_dirs:
            coalition_name = coalition_dir.name.replace("coalition_", "")

            # 加载联盟性能指标
            performance_file = coalition_dir / "performance_metrics.json"
            if performance_file.exists():
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance_data = json.load(f)
                    report_data["coalition_summary"][coalition_name] = performance_data

        # 生成性能分析
        if report_data["coalition_summary"]:
            report_data["performance_analysis"] = self._analyze_coalition_performance(
                report_data["coalition_summary"]
            )

        # 保存报告
        report_file = self.current_experiment_dir / "experiment_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

    def _analyze_coalition_performance(self, coalition_summary: Dict[str, Any]) -> Dict[str, Any]:
        """分析联盟性能"""
        analysis = {
            "best_coalition": None,
            "worst_coalition": None,
            "average_sharpe": 0.0,
            "performance_distribution": {},
            "insights": []
        }

        if not coalition_summary:
            return analysis

        # 找出最佳和最差联盟
        sharpe_ratios = {name: data.get("sharpe_ratio", 0.0)
                        for name, data in coalition_summary.items()}

        if sharpe_ratios:
            best_coalition = max(sharpe_ratios.keys(), key=lambda k: sharpe_ratios[k])
            worst_coalition = min(sharpe_ratios.keys(), key=lambda k: sharpe_ratios[k])

            analysis["best_coalition"] = {
                "name": best_coalition,
                "sharpe_ratio": sharpe_ratios[best_coalition]
            }
            analysis["worst_coalition"] = {
                "name": worst_coalition,
                "sharpe_ratio": sharpe_ratios[worst_coalition]
            }
            analysis["average_sharpe"] = sum(sharpe_ratios.values()) / len(sharpe_ratios)

        return analysis

    def get_coalition_data(self,
                          coalition: Union[Set[str], List[str]],
                          experiment_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取联盟数据

        参数:
            coalition: 联盟成员
            experiment_id: 实验ID，如果为None则使用当前实验

        返回:
            联盟数据字典
        """
        if experiment_id is None:
            if self.current_experiment_dir is None:
                return None
            experiment_dir = self.current_experiment_dir
        else:
            experiment_dir = self.shapley_experiments_dir / experiment_id
            if not experiment_dir.exists():
                return None

        coalition_set = set(coalition) if isinstance(coalition, list) else coalition
        coalition_name = self._generate_coalition_name(coalition_set)
        coalition_dir = experiment_dir / f"coalition_{coalition_name}"

        if not coalition_dir.exists():
            return None

        coalition_data = {}

        # 加载联盟信息
        coalition_info_file = coalition_dir / "coalition_info.json"
        if coalition_info_file.exists():
            with open(coalition_info_file, 'r', encoding='utf-8') as f:
                coalition_data["info"] = json.load(f)

        # 加载性能指标
        performance_file = coalition_dir / "performance_metrics.json"
        if performance_file.exists():
            with open(performance_file, 'r', encoding='utf-8') as f:
                coalition_data["performance"] = json.load(f)

        # 加载交易模拟结果
        simulation_file = coalition_dir / "trading_simulation.json"
        if simulation_file.exists():
            with open(simulation_file, 'r', encoding='utf-8') as f:
                coalition_data["simulation"] = json.load(f)

        return coalition_data

    def list_experiments(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        列出实验

        参数:
            limit: 返回的实验数量限制

        返回:
            实验信息列表
        """
        experiments = []

        experiment_dirs = [d for d in self.shapley_experiments_dir.iterdir()
                          if d.is_dir() and d.name.startswith("experiment_")]

        # 按时间排序
        experiment_dirs.sort(key=lambda x: x.name, reverse=True)

        for experiment_dir in experiment_dirs[:limit]:
            experiment_info_file = experiment_dir / "experiment_info.json"
            if experiment_info_file.exists():
                try:
                    with open(experiment_info_file, 'r', encoding='utf-8') as f:
                        experiment_info = json.load(f)
                        experiments.append({
                            "experiment_id": experiment_dir.name,
                            "start_time": experiment_info.get("start_time", ""),
                            "status": experiment_info.get("status", "unknown"),
                            "coalition_count": len([d for d in experiment_dir.iterdir()
                                                  if d.is_dir() and d.name.startswith("coalition_")])
                        })
                except Exception as e:
                    self.logger.error(f"加载实验信息失败 {experiment_dir.name}: {e}")

        return experiments

    def get_experiment_summary(self, experiment_id: str) -> Optional[Dict[str, Any]]:
        """
        获取实验总结

        参数:
            experiment_id: 实验ID

        返回:
            实验总结数据
        """
        experiment_dir = self.shapley_experiments_dir / experiment_id
        if not experiment_dir.exists():
            return None

        summary = {}

        # 加载实验信息
        experiment_info_file = experiment_dir / "experiment_info.json"
        if experiment_info_file.exists():
            with open(experiment_info_file, 'r', encoding='utf-8') as f:
                summary["experiment_info"] = json.load(f)

        # 加载Shapley结果
        shapley_file = experiment_dir / "shapley_results.json"
        if shapley_file.exists():
            with open(shapley_file, 'r', encoding='utf-8') as f:
                summary["shapley_results"] = json.load(f)

        # 加载实验报告
        report_file = experiment_dir / "experiment_report.json"
        if report_file.exists():
            with open(report_file, 'r', encoding='utf-8') as f:
                summary["experiment_report"] = json.load(f)

        return summary

    def archive_experiment(self, experiment_id: str) -> bool:
        """
        归档实验

        参数:
            experiment_id: 实验ID

        返回:
            是否成功归档
        """
        experiment_dir = self.shapley_experiments_dir / experiment_id
        if not experiment_dir.exists():
            return False

        try:
            archive_dir = self.coalition_archives_dir / experiment_id
            shutil.move(str(experiment_dir), str(archive_dir))
            self.logger.info(f"实验已归档: {experiment_id}")
            return True
        except Exception as e:
            self.logger.error(f"归档实验失败 {experiment_id}: {e}")
            return False
