#!/usr/bin/env python3
"""
测试OPRO系统中NAA代理的新闻数据访问
模拟实际OPRO运行环境
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_opro_system_news_access():
    """测试OPRO系统中的新闻数据访问"""
    print("=" * 80)
    print("🧪 测试OPRO系统新闻数据访问")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        from contribution_assessment.assessor import ContributionAssessor
        from contribution_assessment.llm_interface import LLMInterface
        
        # 创建与OPRO系统相同的配置
        system_config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "simulation_days": None,
            "verbose": True,
            "enable_concurrent_execution": False,
            "fail_on_large_gaps": False,
            "fill_date_gaps": True
        }
        
        print(f"📋 系统配置:")
        for key, value in system_config.items():
            print(f"  {key}: {value}")
        
        # 创建LLM接口（模拟）
        print("\n🔧 创建LLM接口...")
        llm_interface = LLMInterface(provider="zhipuai")
        
        # 创建ContributionAssessor
        print("🔧 创建ContributionAssessor...")
        assessor = ContributionAssessor(
            config=system_config,
            llm_provider="zhipuai",
            enable_opro=True,
            logger=None
        )
        
        print("✅ ContributionAssessor创建成功")
        
        # 检查交易模拟器的配置
        print("\n📊 检查交易模拟器配置...")
        trading_simulator = assessor.trading_simulator
        base_config = trading_simulator.base_config
        
        print(f"交易模拟器基础配置:")
        for key, value in base_config.items():
            print(f"  {key}: {value}")
        
        # 创建一个测试环境来检查新闻数据加载
        print("\n🔍 测试环境新闻数据加载...")
        test_env = trading_simulator._create_trading_environment()
        
        print(f"✅ 测试环境创建成功")
        print(f"📈 股票列表: {test_env.stocks}")
        print(f"📅 交易日数量: {len(test_env.trading_days)}")
        
        # 检查新闻数据
        print(f"\n📰 检查新闻数据...")
        total_news = 0
        for date_str, stocks_news in test_env.news_data.items():
            date_news_count = sum(len(news_list) for news_list in stocks_news.values())
            total_news += date_news_count
            if date_news_count > 0:
                print(f"  {date_str}: {date_news_count} 条新闻")
                for stock, news_list in stocks_news.items():
                    if news_list:
                        print(f"    {stock}: {len(news_list)} 条")
        
        print(f"📊 总新闻数量: {total_news}")
        
        if total_news == 0:
            print("❌ 环境中没有加载到新闻数据")
            return False
        
        # 测试环境状态生成
        print("\n🔄 测试环境状态生成...")
        state, info = test_env.reset()
        
        news_history = state.get('news_history', {})
        print(f"📊 状态中的新闻历史: {len(news_history)} 个日期")
        
        state_total_news = 0
        for date_str, stocks_news in news_history.items():
            date_news_count = sum(len(news_list) for news_list in stocks_news.values())
            state_total_news += date_news_count
            if date_news_count > 0:
                print(f"  状态 {date_str}: {date_news_count} 条新闻")
        
        print(f"📈 状态总新闻数量: {state_total_news}")
        
        if state_total_news == 0:
            print("❌ 环境状态中没有新闻数据")
            return False
        
        # 测试NAA代理处理
        print("\n🤖 测试NAA代理处理...")
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建NAA代理（不使用LLM）
        naa_agent = NewsAnalystAgent()
        
        # 格式化状态
        formatted_state = naa_agent.format_state_for_llm(state)
        print(f"📝 格式化状态长度: {len(formatted_state)} 字符")
        
        # 检查格式化状态中是否包含新闻信息
        if "新闻" in formatted_state or "news" in formatted_state.lower():
            print("✅ 格式化状态包含新闻信息")
            # 显示新闻相关部分
            lines = formatted_state.split('\n')
            news_lines = [line for line in lines if '新闻' in line or 'news' in line.lower()]
            for line in news_lines[:3]:  # 显示前3行
                print(f"     {line}")
        else:
            print("❌ 格式化状态不包含新闻信息")
            print("状态内容预览:")
            lines = formatted_state.split('\n')
            for line in lines[:10]:
                print(f"     {line}")
        
        # 处理NAA代理
        try:
            result = naa_agent.process(state)
            print(f"🔄 NAA处理结果: {result}")
            
            if isinstance(result, dict):
                summary = result.get('summary', result.get('analysis', ''))
                if "无新闻数据可用" in str(summary):
                    print("❌ NAA代理仍然报告无新闻数据可用")
                    return False
                else:
                    print("✅ NAA代理成功处理新闻数据")
                    return True
        except Exception as e:
            print(f"❌ NAA处理失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_opro_run_simulation():
    """测试实际OPRO运行模拟"""
    print("\n" + "=" * 80)
    print("🚀 测试实际OPRO运行模拟")
    print("=" * 80)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 使用与run_opro_system.py相同的配置
        system_config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-01",  # 只测试一天
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "simulation_days": None,
            "verbose": True,
            "enable_concurrent_execution": False,
            "fail_on_large_gaps": False,
            "fill_date_gaps": True
        }
        
        print("🔧 创建ContributionAssessor...")
        assessor = ContributionAssessor(
            config=system_config,
            llm_provider="zhipuai",
            enable_opro=False,  # 禁用OPRO以简化测试
            logger=None
        )
        
        # 运行评估
        print("🔄 运行评估...")
        result = assessor.run(
            agents=None,  # 使用默认代理
            target_agents=["NAA"],  # 只测试NAA
            max_coalitions=1  # 只测试单个联盟
        )
        
        print(f"📊 评估结果: {result}")
        
        if result.get("success", False):
            print("✅ 单日评估成功")
            
            # 检查NAA的具体输出
            shapley_values = result.get("shapley_values", {})
            if "NAA" in shapley_values:
                naa_value = shapley_values["NAA"]
                print(f"📈 NAA Shapley值: {naa_value}")
                
                if naa_value > 0:
                    print("✅ NAA产生了正向贡献，说明新闻数据被正确使用")
                    return True
                else:
                    print("⚠️ NAA贡献值为0或负值，可能存在数据问题")
                    return False
            else:
                print("❌ 结果中没有NAA的Shapley值")
                return False
        else:
            print("❌ 单日评估失败")
            error = result.get("error", "未知错误")
            print(f"错误信息: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始OPRO系统新闻数据访问测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    tests = [
        ("OPRO系统新闻数据访问测试", test_opro_system_news_access),
        ("实际OPRO运行模拟测试", test_actual_opro_run_simulation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🔄 运行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"📊 {test_name}: ❌ 异常 - {e}")
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📋 测试结果汇总")
    print("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed < total:
        print("\n💡 问题分析:")
        print("如果新闻数据访问测试失败，可能的原因包括:")
        print("1. StockTradingEnv的新闻数据加载逻辑问题")
        print("2. NAA代理的状态格式化或处理逻辑问题")
        print("3. 配置参数传递问题")
        print("4. 文件路径或权限问题")
    else:
        print("\n🎉 所有测试通过！OPRO系统新闻数据访问正常")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
