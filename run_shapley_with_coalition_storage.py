#!/usr/bin/env python3
"""
使用新联盟存储系统运行Shapley值计算

这个脚本演示如何使用新的联盟特定存储结构来运行完整的Shapley值计算，
每个联盟都会创建以代理名称命名的文件夹来存储实验过程和结果。

用法:
    python run_shapley_with_coalition_storage.py [--config config.json] [--agents NAA,TAA,TRA]
"""

import sys
import os
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from contribution_assessment.assessor import ContributionAssessor

def setup_logging(log_level=logging.INFO):
    """设置日志"""
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'shapley_coalition_storage_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path=None):
    """加载配置文件"""
    default_config = {
        "start_date": "2025-01-01",
        "end_date": "2025-01-10",
        "initial_balance": 100000,
        "ticker": "AAPL",
        "risk_free_rate": 0.02,
        "weekly_evaluation_enabled": True,
        "trading_days_per_week": 5,
        "enable_concurrent_execution": True
    }
    
    if config_path and Path(config_path).exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            user_config = json.load(f)
            default_config.update(user_config)
    
    return default_config

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="使用联盟存储系统运行Shapley值计算",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    # 使用默认配置运行
    python run_shapley_with_coalition_storage.py
    
    # 指定配置文件
    python run_shapley_with_coalition_storage.py --config my_config.json
    
    # 指定目标代理
    python run_shapley_with_coalition_storage.py --agents NAA,TAA,TRA
    
    # 限制联盟数量
    python run_shapley_with_coalition_storage.py --max-coalitions 10
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径 (JSON格式)'
    )
    
    parser.add_argument(
        '--agents', '-a',
        type=str,
        help='目标代理列表，用逗号分隔 (例如: NAA,TAA,TRA)'
    )
    
    parser.add_argument(
        '--max-coalitions', '-m',
        type=int,
        help='最大联盟数量限制'
    )
    
    parser.add_argument(
        '--log-level', '-l',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='干运行模式，只显示配置不执行计算'
    )
    
    return parser.parse_args()

def print_configuration(config, target_agents, max_coalitions, logger):
    """打印配置信息"""
    logger.info("=" * 60)
    logger.info("Shapley值计算配置")
    logger.info("=" * 60)
    logger.info(f"交易时间段: {config['start_date']} 到 {config['end_date']}")
    logger.info(f"股票代码: {config['ticker']}")
    logger.info(f"初始资金: ${config['initial_balance']:,}")
    logger.info(f"无风险利率: {config['risk_free_rate']:.2%}")
    logger.info(f"目标代理: {target_agents}")
    logger.info(f"最大联盟数: {max_coalitions or '无限制'}")
    logger.info(f"周期性评估: {'启用' if config.get('weekly_evaluation_enabled') else '禁用'}")
    logger.info(f"并发执行: {'启用' if config.get('enable_concurrent_execution') else '禁用'}")
    logger.info("=" * 60)

def run_shapley_calculation(config, target_agents, max_coalitions, logger):
    """运行Shapley值计算"""
    logger.info("开始Shapley值计算...")
    
    # 创建贡献度评估器
    assessor = ContributionAssessor(
        config=config,
        logger=logger,
        enable_opro=False  # 暂时禁用OPRO以专注于存储功能
    )
    
    # 运行评估
    result = assessor.run(
        target_agents=target_agents,
        max_coalitions=max_coalitions
    )
    
    return result

def print_results(result, logger):
    """打印结果"""
    logger.info("=" * 60)
    logger.info("Shapley值计算结果")
    logger.info("=" * 60)
    
    if result.get("success"):
        shapley_values = result.get("shapley_values", {})
        logger.info("Shapley值:")
        for agent, value in sorted(shapley_values.items()):
            logger.info(f"  {agent}: {value:.4f}")
        
        summary = result.get("summary", {})
        logger.info(f"\n总结:")
        logger.info(f"  总代理数: {summary.get('total_agents', 0)}")
        logger.info(f"  总联盟数: {summary.get('coalition_generations', 0)}")
        logger.info(f"  执行时间: {result.get('execution_time', 0):.2f}秒")
        logger.info(f"  最佳贡献者: {summary.get('top_contributor', 'N/A')}")
        logger.info(f"  系统总价值: {summary.get('total_system_value', 0):.4f}")
        
        # 显示存储信息
        logger.info(f"\n存储信息:")
        logger.info(f"  实验数据已保存到联盟特定文件夹")
        logger.info(f"  查看路径: data/shapley_experiments/latest_experiment/")
        logger.info(f"  每个联盟都有独立的文件夹，包含:")
        logger.info(f"    - coalition_info.json (联盟基本信息)")
        logger.info(f"    - trading_simulation.json (交易模拟结果)")
        logger.info(f"    - performance_metrics.json (性能指标)")
        logger.info(f"    - daily_data/daily_returns.json (每日收益数据)")
        logger.info(f"    - agent_logs/ (各代理的日志目录)")
        
    else:
        logger.error(f"计算失败: {result.get('error', '未知错误')}")
    
    logger.info("=" * 60)

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    log_level = getattr(logging, args.log_level.upper())
    logger = setup_logging(log_level)
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 解析目标代理
        if args.agents:
            target_agents = [agent.strip() for agent in args.agents.split(',')]
        else:
            target_agents = ["NAA", "TAA", "FAA", "TRA"]  # 默认代理
        
        # 打印配置
        print_configuration(config, target_agents, args.max_coalitions, logger)
        
        # 干运行模式
        if args.dry_run:
            logger.info("干运行模式，不执行实际计算")
            return 0
        
        # 运行Shapley计算
        result = run_shapley_calculation(config, target_agents, args.max_coalitions, logger)
        
        # 打印结果
        print_results(result, logger)
        
        return 0 if result.get("success") else 1
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
