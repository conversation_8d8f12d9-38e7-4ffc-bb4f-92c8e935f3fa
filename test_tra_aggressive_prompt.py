#!/usr/bin/env python3
"""
测试TRA代理的积极交易提示词
验证TRA代理是否使用了积极的交易策略而非保守的持有策略
"""

import sys
import os
import logging
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tra_prompt():
    """测试TRA代理的提示词内容"""
    print("=" * 80)
    print("🔍 TRA代理提示词测试")
    print("=" * 80)
    
    try:
        # 导入TRA代理
        from agents.trader_agent import TraderAgent
        
        # 创建TRA代理实例
        tra_agent = TraderAgent()
        
        # 获取提示词模板
        prompt_template = tra_agent.get_prompt_template()
        
        print("📋 当前TRA代理提示词模板:")
        print("-" * 60)
        print(prompt_template)
        print("-" * 60)
        
        # 检查积极交易关键词
        aggressive_keywords = [
            "积极交易",
            "主动交易", 
            "避免过度保守",
            "积极寻找交易机会",
            "不交易也是一种风险",
            "buy信号 >= 3个时，执行买入",
            "sell信号 >= 3个时，执行卖出",
            "buy信号 >= 2个且sell信号 <= 1个时，执行买入",
            "sell信号 >= 2个且buy信号 <= 1个时，执行卖出"
        ]
        
        conservative_keywords = [
            "如果信号冲突严重，选择持有",
            "考虑风险管理和仓位控制",
            "保守",
            "谨慎"
        ]
        
        print("\n✅ 积极交易关键词检查:")
        aggressive_found = 0
        for keyword in aggressive_keywords:
            if keyword in prompt_template:
                print(f"  ✓ 找到: {keyword}")
                aggressive_found += 1
            else:
                print(f"  ✗ 未找到: {keyword}")
        
        print(f"\n积极关键词覆盖率: {aggressive_found}/{len(aggressive_keywords)} ({aggressive_found/len(aggressive_keywords)*100:.1f}%)")
        
        print("\n❌ 保守交易关键词检查:")
        conservative_found = 0
        for keyword in conservative_keywords:
            if keyword in prompt_template:
                print(f"  ⚠️ 发现保守关键词: {keyword}")
                conservative_found += 1
            else:
                print(f"  ✓ 未发现: {keyword}")
        
        print(f"\n保守关键词数量: {conservative_found}")
        
        # 评估结果
        print("\n" + "=" * 80)
        print("📊 评估结果:")
        
        if aggressive_found >= 6 and conservative_found == 0:
            print("✅ TRA代理提示词已成功修改为积极交易策略")
            print("   - 包含充分的积极交易指导")
            print("   - 没有保守持有的倾向")
            return True
        elif aggressive_found >= 3:
            print("⚠️ TRA代理提示词部分积极，但可能需要进一步优化")
            if conservative_found > 0:
                print("   - 仍包含一些保守关键词")
            return False
        else:
            print("❌ TRA代理提示词仍然过于保守")
            print("   - 缺乏积极交易指导")
            print("   - 需要更新提示词模板")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_assessor_default_prompt():
    """测试Assessor中的默认TRA提示词"""
    print("\n" + "=" * 80)
    print("🔍 Assessor默认TRA提示词测试")
    print("=" * 80)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 创建一个临时的assessor实例
        assessor = ContributionAssessor({})
        
        # 获取默认TRA提示词
        default_tra_prompt = assessor._get_default_prompt_for_agent("TRA")
        
        print("📋 Assessor中的默认TRA提示词:")
        print("-" * 60)
        print(default_tra_prompt)
        print("-" * 60)
        
        # 检查是否包含积极交易内容
        if "积极交易" in default_tra_prompt or "主动交易" in default_tra_prompt:
            print("✅ Assessor默认提示词已更新为积极交易策略")
            return True
        else:
            print("❌ Assessor默认提示词仍然保守")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始TRA代理积极交易提示词测试")
    
    # 测试TraderAgent类的提示词
    tra_result = test_tra_prompt()
    
    # 测试Assessor中的默认提示词
    assessor_result = test_assessor_default_prompt()
    
    print("\n" + "=" * 80)
    print("📋 最终测试结果:")
    print(f"  TraderAgent类提示词: {'✅ 通过' if tra_result else '❌ 需要修复'}")
    print(f"  Assessor默认提示词: {'✅ 通过' if assessor_result else '❌ 需要修复'}")
    
    if tra_result and assessor_result:
        print("\n🎉 所有测试通过！TRA代理已成功修改为积极交易策略")
        return True
    else:
        print("\n⚠️ 部分测试未通过，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
