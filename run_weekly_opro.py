#!/usr/bin/env python3
"""
每周OPRO运行脚本 (Weekly OPRO Runner)

用于运行每周OPRO提示词优化和A/B测试的自动化脚本。

使用示例:
    # 运行每周优化
    python run_weekly_opro.py --mode optimize --week-start 2025-01-01

    # 运行A/B测试
    python run_weekly_opro.py --mode ab-test --week-start 2025-01-08

    # 生成每周报告
    python run_weekly_opro.py --mode report --week-start 2025-01-01

    # 查看优化历史
    python run_weekly_opro.py --mode history --weeks-back 4
"""

import argparse
import json
import logging
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from contribution_assessment.weekly_opro_manager import WeeklyOPROManager
from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.opro_optimizer import OPROOptimizer

def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'logs/weekly_opro_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_file: str = "config/opro_config.json") -> dict:
    """加载配置文件"""
    if not os.path.exists(config_file):
        # 使用默认配置
        return {
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "stocks": ["AAPL", "MSFT"],
            "starting_cash": 1000000,
            "enable_concurrent_execution": True,
            "opro": {
                "optimization_frequency": "weekly",
                "candidates_per_generation": 8,
                "historical_weeks_to_consider": 10,
                "temperature": 1.0,
                "min_improvement_threshold": 0.01
            }
        }
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)

def initialize_components(config: dict, logger: logging.Logger):
    """初始化组件"""
    logger.info("初始化OPRO系统组件...")
    
    try:
        # 初始化评估器
        assessor = ContributionAssessor(
            config=config,
            llm_provider="zhipuai",
            enable_opro=True,
            opro_config=config.get("opro", {})
        )
        
        # 初始化OPRO优化器
        opro_optimizer = OPROOptimizer(
            config=config.get("opro", {}),
            llm_interface=assessor.llm_interface,
            historical_score_manager=assessor.historical_score_manager,
            logger=logger
        )
        
        # 初始化每周OPRO管理器
        weekly_manager = WeeklyOPROManager(
            base_data_dir="data",
            opro_optimizer=opro_optimizer,
            assessor=assessor,
            logger=logger
        )
        
        logger.info("组件初始化完成")
        return assessor, opro_optimizer, weekly_manager
        
    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        sys.exit(1)

def run_weekly_optimization(weekly_manager: WeeklyOPROManager, 
                          week_start: str, 
                          target_agents: list,
                          force: bool = False) -> dict:
    """运行每周优化"""
    print(f"\n🔄 开始每周优化 - 周期: {week_start}")
    print(f"目标代理: {', '.join(target_agents)}")
    
    result = weekly_manager.run_weekly_optimization_cycle(
        target_agents=target_agents,
        current_week_start=week_start,
        force_optimization=force
    )
    
    if result["status"] == "success":
        print("✅ 每周优化完成")
        print(f"优化代理数量: {len(result['optimized_agents'])}")
        print(f"版本信息已保存")
        print(f"A/B测试配置已准备: {result['ab_test_config']['test_week_start']}")
    elif result["status"] == "skipped":
        print("⏭️ 本周已完成优化，跳过")
    else:
        print(f"❌ 优化失败: {result.get('error', '未知错误')}")
    
    return result

def run_ab_test(weekly_manager: WeeklyOPROManager, 
                test_week_start: str, 
                target_agents: list,
                test_duration: int = 7) -> dict:
    """运行A/B测试"""
    print(f"\n🧪 开始A/B测试 - 测试周: {test_week_start}")
    print(f"目标代理: {', '.join(target_agents)}")
    print(f"测试持续时间: {test_duration}天")
    
    result = weekly_manager.run_weekly_ab_test(
        target_agents=target_agents,
        test_week_start=test_week_start,
        test_duration_days=test_duration
    )
    
    if result["status"] == "success":
        print("✅ A/B测试完成")
        
        performance = result["performance_comparison"]
        improvement = performance.get("improvement_analysis", {})
        
        print(f"夏普率改善: {improvement.get('primary_metric_improvement', 0):.4f}")
        print(f"整体改善: {'是' if improvement.get('overall_improvement', False) else '否'}")
        print(f"建议: {improvement.get('recommendation', 'neutral')}")
        
        adoption = result["adoption_decision"]
        print(f"采用决策: {'采用优化版本' if adoption.get('adopt_optimized_version', False) else '保持原版本'}")
        print(f"决策理由: {adoption.get('decision_reason', '')}")
    else:
        print(f"❌ A/B测试失败: {result.get('error', '未知错误')}")
    
    return result

def generate_weekly_report(weekly_manager: WeeklyOPROManager, week_start: str) -> dict:
    """生成每周报告"""
    print(f"\n📊 生成每周报告 - 周期: {week_start}")
    
    report = weekly_manager.generate_weekly_report(week_start)
    
    print("=" * 50)
    print(f"每周OPRO报告 - {week_start}")
    print("=" * 50)
    
    # 优化总结
    opt_summary = report.get("optimization_summary", {})
    if opt_summary:
        print(f"\n📈 优化总结:")
        print(f"  优化代理: {', '.join(opt_summary.get('optimized_agents', []))}")
        print(f"  成功率: {opt_summary.get('success_count', 0)}/{opt_summary.get('total_agents', 0)}")
        print(f"  优化时间: {opt_summary.get('optimization_timestamp', '')}")
    
    # A/B测试总结
    ab_summary = report.get("ab_test_summary", {})
    if ab_summary:
        print(f"\n🧪 A/B测试总结:")
        print(f"  测试持续时间: {ab_summary.get('test_duration', 0)}天")
        print(f"  整体改善: {'是' if ab_summary.get('overall_improvement', False) else '否'}")
        print(f"  夏普率改善: {ab_summary.get('sharpe_improvement', 0):.4f}")
        print(f"  建议: {ab_summary.get('recommendation', 'neutral')}")
        
        adoption = ab_summary.get("adoption_decision", {})
        if adoption:
            print(f"  采用决策: {'采用' if adoption.get('adopt_optimized_version', False) else '保持原版本'}")
    
    # 建议
    recommendations = report.get("recommendations", [])
    if recommendations:
        print(f"\n💡 建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    print("=" * 50)
    
    return report

def show_optimization_history(weekly_manager: WeeklyOPROManager, weeks_back: int = 4):
    """显示优化历史"""
    print(f"\n📚 最近{weeks_back}周优化历史")
    
    history = weekly_manager.get_weekly_optimization_history(weeks_back)
    
    if not history:
        print("暂无优化历史记录")
        return
    
    for week_data in history:
        week_start = week_data["week_start"]
        print(f"\n📅 周期: {week_start}")
        
        if "optimization" in week_data:
            opt_data = week_data["optimization"]
            agents = opt_data.get("target_agents", [])
            print(f"  优化代理: {', '.join(agents)}")
            print(f"  优化时间: {opt_data.get('optimization_timestamp', '')}")
        
        if "ab_test" in week_data:
            ab_data = week_data["ab_test"]
            performance = ab_data.get("performance_comparison", {})
            improvement = performance.get("improvement_analysis", {})
            print(f"  A/B测试: 夏普率改善 {improvement.get('primary_metric_improvement', 0):.4f}")
            print(f"  建议: {improvement.get('recommendation', 'neutral')}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="每周OPRO运行脚本")
    
    parser.add_argument("--mode", type=str, required=True,
                       choices=["optimize", "ab-test", "report", "history"],
                       help="运行模式")
    parser.add_argument("--week-start", type=str, 
                       help="周开始日期 (YYYY-MM-DD)")
    parser.add_argument("--agents", type=str, default="NAA,TAA,FAA,TRA",
                       help="目标代理列表，逗号分隔")
    parser.add_argument("--test-duration", type=int, default=7,
                       help="A/B测试持续天数")
    parser.add_argument("--weeks-back", type=int, default=4,
                       help="查看历史的周数")
    parser.add_argument("--force", action="store_true",
                       help="强制执行优化")
    parser.add_argument("--config", type=str, default="config/opro_config.json",
                       help="配置文件路径")
    parser.add_argument("--verbose", action="store_true",
                       help="详细日志")
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.verbose)
    
    # 验证参数
    if args.mode in ["optimize", "ab-test", "report"] and not args.week_start:
        print("错误: 该模式需要指定 --week-start 参数")
        sys.exit(1)
    
    # 解析代理列表
    target_agents = [agent.strip() for agent in args.agents.split(",")]
    
    # 加载配置
    config = load_config(args.config)
    
    # 初始化组件
    assessor, opro_optimizer, weekly_manager = initialize_components(config, logger)
    
    try:
        if args.mode == "optimize":
            result = run_weekly_optimization(
                weekly_manager, args.week_start, target_agents, args.force
            )
            
        elif args.mode == "ab-test":
            result = run_ab_test(
                weekly_manager, args.week_start, target_agents, args.test_duration
            )
            
        elif args.mode == "report":
            result = generate_weekly_report(weekly_manager, args.week_start)
            
        elif args.mode == "history":
            show_optimization_history(weekly_manager, args.weeks_back)
            result = {"status": "success"}
        
        # 保存结果
        if args.mode != "history":
            result_file = f"data/weekly_experiments/result_{args.mode}_{args.week_start or 'latest'}.json"
            os.makedirs(os.path.dirname(result_file), exist_ok=True)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n结果已保存到: {result_file}")
        
        print("\n✅ 任务完成")
        
    except KeyboardInterrupt:
        print("\n⏹️ 任务被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        print(f"\n❌ 任务失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
