# 联盟存储系统 (Coalition Storage System)

## 概述

本文档描述了新实现的联盟特定存储系统，该系统为每个联盟创建以代理名称命名的文件夹，存储实验过程、夏普率、收益率等详细数据。

## 系统架构

### 核心组件

1. **CoalitionStorageManager** - 联盟存储管理器
   - 负责创建和管理联盟特定的存储结构
   - 处理实验数据的保存和检索
   - 管理Shapley值计算结果

2. **TradingSimulator** - 交易模拟器 (已增强)
   - 集成了存储管理器
   - 自动保存联盟模拟结果

3. **ContributionAssessor** - 贡献度评估器 (已增强)
   - 协调整个存储流程
   - 管理实验生命周期

## 存储结构

```
data/
├── shapley_experiments/
│   ├── experiment_{timestamp}/
│   │   ├── coalition_{NAA_TAA_TRA}/
│   │   │   ├── coalition_info.json          # 联盟基本信息
│   │   │   ├── trading_simulation.json      # 交易模拟结果
│   │   │   ├── performance_metrics.json     # 性能指标
│   │   │   ├── daily_data/
│   │   │   │   └── daily_returns.json       # 每日收益数据
│   │   │   ├── performance_analysis/        # 性能分析目录
│   │   │   └── agent_logs/                  # 代理日志目录
│   │   │       ├── NAA/
│   │   │       │   ├── inputs.json
│   │   │       │   ├── outputs.json
│   │   │       │   └── prompts.json
│   │   │       ├── TAA/
│   │   │       └── TRA/
│   │   ├── coalition_{NAA_TRA}/
│   │   ├── coalition_{TAA_TRA}/
│   │   ├── experiment_info.json             # 实验基本信息
│   │   ├── shapley_results.json             # Shapley值计算结果
│   │   └── experiment_report.json           # 实验报告
│   ├── latest_experiment -> experiment_{latest_timestamp}
│   └── ...
├── coalition_archives/                      # 归档的实验
└── performance_summaries/                   # 性能总结
```

## 主要功能

### 1. 实验管理

```python
# 开始新实验
experiment_id = storage_manager.start_new_experiment({
    "target_agents": ["NAA", "TAA", "TRA"],
    "description": "测试联盟存储系统"
})

# 完成实验
storage_manager.finish_experiment({
    "total_coalitions": 7,
    "execution_time": 120.5
})
```

### 2. 联盟存储

```python
# 创建联盟存储
coalition_dir = storage_manager.create_coalition_storage(
    coalition={"NAA", "TAA", "TRA"},
    coalition_info={"coalition_type": "full_analysis"}
)

# 保存模拟结果
storage_manager.save_coalition_simulation_result(
    coalition={"NAA", "TAA", "TRA"},
    simulation_result={
        "sharpe_ratio": 0.85,
        "daily_returns": [0.01, -0.005, 0.02, ...],
        "total_days": 50
    }
)
```

### 3. Shapley结果保存

```python
# 保存Shapley计算结果
storage_manager.save_shapley_calculation_result(
    shapley_values={"NAA": 0.15, "TAA": 0.12, "TRA": 0.25},
    coalition_values={
        frozenset(["NAA", "TRA"]): 0.35,
        frozenset(["TAA", "TRA"]): 0.32,
        frozenset(["NAA", "TAA", "TRA"]): 0.52
    },
    calculation_metadata={"method": "standard_shapley"}
)
```

### 4. 数据检索

```python
# 获取联盟数据
coalition_data = storage_manager.get_coalition_data(
    coalition={"NAA", "TRA"},
    experiment_id="experiment_20250705_210710"
)

# 列出实验
experiments = storage_manager.list_experiments(limit=10)

# 获取实验总结
summary = storage_manager.get_experiment_summary("experiment_20250705_210710")
```

## 数据格式

### coalition_info.json
```json
{
  "coalition_name": "NAA_TAA_TRA",
  "coalition_members": ["NAA", "TAA", "TRA"],
  "coalition_size": 3,
  "creation_time": "2025-07-05T21:07:10.349677",
  "experiment_id": "experiment_20250705_210710"
}
```

### performance_metrics.json
```json
{
  "timestamp": "2025-07-05T21:07:10.350677",
  "sharpe_ratio": 0.8,
  "total_return": 0.3,
  "volatility": 0.0116,
  "max_drawdown": 0.01,
  "win_rate": 0.6,
  "total_trades": 0
}
```

### shapley_results.json
```json
{
  "experiment_id": "experiment_20250705_210710",
  "calculation_time": "2025-07-05T21:07:10.351681",
  "shapley_values": {
    "NAA": 0.15,
    "TAA": 0.12,
    "TRA": 0.25
  },
  "coalition_values": {
    "NAA_TRA": 0.35,
    "TAA_TRA": 0.32,
    "NAA_TAA_TRA": 0.52
  },
  "metadata": {
    "calculation_method": "standard_shapley",
    "total_coalitions": 3
  },
  "summary": {
    "total_agents": 3,
    "total_coalitions": 3,
    "average_shapley": 0.173,
    "max_shapley": 0.25,
    "min_shapley": 0.12
  }
}
```

## 使用示例

### 基本使用

```bash
# 运行测试
python test_coalition_storage.py

# 使用新存储系统运行Shapley计算
python run_shapley_with_coalition_storage.py --config test_config.json --agents NAA,TAA,TRA
```

### 高级配置

```bash
# 限制联盟数量
python run_shapley_with_coalition_storage.py --max-coalitions 10

# 干运行模式
python run_shapley_with_coalition_storage.py --dry-run

# 调试模式
python run_shapley_with_coalition_storage.py --log-level DEBUG
```

## 优势

1. **组织性** - 每个联盟都有独立的文件夹，便于管理和分析
2. **可追溯性** - 完整记录实验过程和结果
3. **可扩展性** - 支持添加新的数据类型和分析功能
4. **兼容性** - 与现有系统无缝集成
5. **性能** - 支持并发操作和大规模实验

## 注意事项

1. 确保有足够的磁盘空间存储实验数据
2. 定期清理或归档旧实验以节省空间
3. 在生产环境中考虑数据备份策略
4. 监控存储性能，特别是在大规模实验中

## 下一步计划

1. 实现TRA代理提示词优化以增强交易积极性
2. 集成A/B测试框架进行提示词评估
3. 添加可视化工具展示联盟性能对比
4. 实现自动化报告生成功能
