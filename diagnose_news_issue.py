#!/usr/bin/env python3
"""
诊断NAA代理新闻数据访问问题
"""

import sys
import os
import json
from datetime import datetime, date
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_news_data_availability():
    """检查新闻数据可用性"""
    print("=" * 80)
    print("🔍 新闻数据可用性诊断")
    print("=" * 80)
    
    # 1. 检查data/news目录
    news_dir = Path("data/news")
    if not news_dir.exists():
        print("❌ data/news 目录不存在")
        return False
    
    print(f"✅ data/news 目录存在")
    
    # 2. 统计新闻文件数量
    news_files = list(news_dir.glob("alpha_news_*.json"))
    print(f"📁 发现 {len(news_files)} 个新闻文件")
    
    if len(news_files) == 0:
        print("❌ 没有找到新闻文件")
        return False
    
    # 3. 检查最近几天的新闻文件
    recent_dates = ["2025-01-01", "2025-01-02", "2025-01-03"]
    for date_str in recent_dates:
        news_file = news_dir / f"alpha_news_{date_str}.json"
        if news_file.exists():
            try:
                with open(news_file, 'r', encoding='utf-8') as f:
                    news_data = json.load(f)
                print(f"✅ {date_str}: {len(news_data)} 条新闻")
                
                # 检查第一条新闻的格式
                if news_data:
                    first_news = news_data[0]
                    print(f"   示例标题: {first_news.get('title', 'N/A')[:50]}...")
                    print(f"   情绪标签: {first_news.get('overall_sentiment_label', 'N/A')}")
                    print(f"   情绪评分: {first_news.get('overall_sentiment_score', 'N/A')}")
                    
            except Exception as e:
                print(f"❌ {date_str}: 文件读取失败 - {e}")
        else:
            print(f"⚠️ {date_str}: 文件不存在")
    
    return True

def test_stock_trading_env_news_loading():
    """测试StockTradingEnv的新闻加载功能"""
    print("\n" + "=" * 80)
    print("🧪 测试StockTradingEnv新闻加载")
    print("=" * 80)

    try:
        from stock_trading_env import StockTradingEnv

        # 创建测试环境配置
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-03",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "fail_on_large_gaps": False,
            "fill_date_gaps": False,
            "max_date_gap_days": 7
        }

        env = StockTradingEnv(config)
        
        print("✅ StockTradingEnv 创建成功")
        
        # 检查新闻数据是否被加载
        state, info = env.reset()
        news_history = state.get('news_history', {})
        
        print(f"📊 新闻历史数据包含 {len(news_history)} 个日期")
        
        total_news = 0
        for date_str, stocks_news in news_history.items():
            date_news_count = sum(len(news_list) for news_list in stocks_news.values())
            total_news += date_news_count
            print(f"   {date_str}: {date_news_count} 条新闻")
            
            # 显示AAPL的新闻详情
            if "AAPL" in stocks_news and stocks_news["AAPL"]:
                aapl_news = stocks_news["AAPL"]
                print(f"     AAPL新闻: {len(aapl_news)} 条")
                if aapl_news:
                    first_news = aapl_news[0]
                    print(f"     示例: {first_news.get('title', 'N/A')[:50]}...")
        
        print(f"📈 总新闻数量: {total_news}")
        
        if total_news == 0:
            print("❌ 没有加载到任何新闻数据")
            return False
        else:
            print("✅ 新闻数据加载成功")
            return True
            
    except Exception as e:
        print(f"❌ StockTradingEnv测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_naa_agent_processing():
    """测试NAA代理处理"""
    print("\n" + "=" * 80)
    print("🤖 测试NAA代理处理")
    print("=" * 80)
    
    try:
        from agents.analyst_agents import NewsAnalystAgent
        from stock_trading_env import StockTradingEnv
        
        # 创建测试环境配置
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-01",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "fail_on_large_gaps": False,
            "fill_date_gaps": False,
            "max_date_gap_days": 7
        }

        env = StockTradingEnv(config)

        state, _ = env.reset()
        
        # 创建NAA代理
        naa_agent = NewsAnalystAgent()
        
        print("✅ NAA代理创建成功")
        
        # 检查状态中的新闻数据
        news_history = state.get('news_history', {})
        print(f"📊 状态中的新闻历史: {len(news_history)} 个日期")
        
        for date_str, stocks_news in news_history.items():
            print(f"   {date_str}:")
            for stock, news_list in stocks_news.items():
                print(f"     {stock}: {len(news_list)} 条新闻")
        
        # 格式化状态给LLM
        formatted_state = naa_agent.format_state_for_llm(state)
        print(f"📝 格式化状态长度: {len(formatted_state)} 字符")
        
        # 检查格式化状态中是否包含新闻信息
        if "新闻" in formatted_state or "news" in formatted_state.lower():
            print("✅ 格式化状态包含新闻信息")
            # 显示新闻相关部分
            lines = formatted_state.split('\n')
            news_lines = [line for line in lines if '新闻' in line or 'news' in line.lower()]
            for line in news_lines[:5]:  # 显示前5行
                print(f"     {line}")
        else:
            print("❌ 格式化状态不包含新闻信息")
            # 显示状态的前几行
            lines = formatted_state.split('\n')
            print("状态内容预览:")
            for line in lines[:10]:
                print(f"     {line}")
        
        # 尝试处理（不使用LLM）
        try:
            result = naa_agent.process(state)
            print(f"🔄 NAA处理结果: {result}")
            
            if isinstance(result, dict):
                summary = result.get('summary', '')
                if "无新闻数据可用" in summary:
                    print("❌ NAA代理报告无新闻数据可用")
                    return False
                else:
                    print("✅ NAA代理成功处理新闻数据")
                    return True
            
        except Exception as e:
            print(f"❌ NAA处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ NAA代理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_news_extraction_logic():
    """检查新闻提取逻辑"""
    print("\n" + "=" * 80)
    print("🔍 检查新闻提取逻辑")
    print("=" * 80)
    
    try:
        from stock_trading_env import StockTradingEnv
        
        # 创建环境实例来测试提取逻辑
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-01",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "fail_on_large_gaps": False
        }
        env = StockTradingEnv(config)
        
        # 读取一个新闻文件进行测试
        news_file = Path("data/news/alpha_news_2025-01-01.json")
        if news_file.exists():
            with open(news_file, 'r', encoding='utf-8') as f:
                news_data = json.load(f)
            
            print(f"📰 测试新闻文件: {len(news_data)} 条新闻")
            
            # 测试前几条新闻的提取
            target_stocks = ["AAPL"]
            for i, news_item in enumerate(news_data[:5]):
                title = news_item.get('title', '')
                print(f"\n新闻 {i+1}:")
                print(f"  标题: {title}")
                
                # 调用提取方法
                relevant_stocks = env._extract_stocks_from_news(news_item, target_stocks)
                print(f"  提取到的相关股票: {relevant_stocks}")
                
                # 检查是否包含AAPL相关关键词
                title_upper = title.upper()
                if "AAPL" in title_upper or "APPLE" in title_upper:
                    print(f"  ✅ 标题包含AAPL相关关键词")
                else:
                    print(f"  ⚠️ 标题不包含AAPL相关关键词")
                    
                    # 检查是否包含市场关键词
                    market_keywords = ['S&P 500', 'NASDAQ', 'DOW JONES', 'MARKET', 'STOCKS', 'EQUITY']
                    has_market_keyword = any(keyword in title_upper for keyword in market_keywords)
                    if has_market_keyword:
                        print(f"  ✅ 标题包含市场关键词")
                    else:
                        print(f"  ⚠️ 标题不包含市场关键词")
            
            return True
        else:
            print("❌ 测试新闻文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 新闻提取逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🚀 开始NAA代理新闻数据访问问题诊断")
    print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行诊断测试
    tests = [
        ("新闻数据可用性检查", check_news_data_availability),
        ("StockTradingEnv新闻加载测试", test_stock_trading_env_news_loading),
        ("新闻提取逻辑检查", check_news_extraction_logic),
        ("NAA代理处理测试", test_naa_agent_processing)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🔄 运行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"📊 {test_name}: ❌ 异常 - {e}")
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📋 诊断结果汇总")
    print("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    # 提供解决方案建议
    if passed < total:
        print("\n" + "=" * 80)
        print("💡 问题解决建议")
        print("=" * 80)
        
        if not results.get("新闻数据可用性检查", False):
            print("1. 新闻数据文件问题:")
            print("   - 检查data/news目录是否存在")
            print("   - 确认alpha_news_*.json文件格式正确")
            
        if not results.get("StockTradingEnv新闻加载测试", False):
            print("2. StockTradingEnv加载问题:")
            print("   - 检查_load_news_data方法的实现")
            print("   - 确认新闻文件路径配置正确")
            
        if not results.get("新闻提取逻辑检查", False):
            print("3. 新闻提取逻辑问题:")
            print("   - 检查_extract_stocks_from_news方法")
            print("   - 确认股票关键词匹配逻辑")
            
        if not results.get("NAA代理处理测试", False):
            print("4. NAA代理处理问题:")
            print("   - 检查NAA代理的状态格式化逻辑")
            print("   - 确认新闻数据传递到代理")
    else:
        print("\n🎉 所有诊断测试通过！新闻数据访问正常")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
