2025-07-05 21:48:06,233 - __main__ - INFO - ====================================================================================================
2025-07-05 21:48:06,233 - __main__ - INFO - OPRO系统启动
2025-07-05 21:48:06,233 - __main__ - INFO - ====================================================================================================
2025-07-05 21:48:06,233 - __main__ - INFO - 运行模式: integrated
2025-07-05 21:48:06,233 - __main__ - INFO - LLM提供商: zhipuai
2025-07-05 21:48:06,233 - __main__ - INFO - OPRO启用: True
2025-07-05 21:48:06,233 - __main__ - INFO - 数据存储启用: True
2025-07-05 21:48:06,233 - __main__ - INFO - 代理日志记录启用: True
2025-07-05 21:48:06,235 - __main__ - INFO - 初始化系统...
2025-07-05 21:48:06,235 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:48:06,236 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,237 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:06,356 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,357 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:06,474 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,475 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:06,606 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:48:06,607 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:48:06,607 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-05 21:48:06,607 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:48:06,607 - __main__ - INFO - 设置实验日期: 2025-07-05
2025-07-05 21:48:06,608 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-05)
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:48:06,608 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:48:06,614 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:48:06,615 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:48:06,615 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:48:06,615 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-05 21:48:06,615 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-05 21:48:06,616 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:48:06,617 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:48:06,617 - __main__ - INFO - 可视化管理器初始化完成
2025-07-05 21:48:06,623 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-05 21:48:06,624 - __main__ - INFO - A/B测试框架初始化完成
2025-07-05 21:48:06,624 - __main__ - INFO - 数据分析工具初始化完成
2025-07-05 21:48:06,624 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-05 21:48:06,625 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-05 21:48:06,625 - __main__ - INFO - 备份管理器初始化完成
2025-07-05 21:48:06,625 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-05 21:48:06,625 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-05 21:48:06,625 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-05 21:48:06,626 - __main__ - INFO - 分析缓存初始化完成
2025-07-05 21:48:06,626 - __main__ - INFO - 联盟管理器初始化完成
2025-07-05 21:48:06,626 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:48:06,626 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:48:06,626 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:48:06,627 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:48:06,628 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:48:06,629 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:48:06,629 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:48:06,630 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:48:06,630 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:48:06,631 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-05 21:48:06,631 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:48:06,631 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,632 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:06,636 - __main__ - ERROR - 创建数据备份失败: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'data/backups\\backup_20250705_214806\\comprehensive_storage.db'
2025-07-05 21:48:06,637 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250705_214806\\.\\prompts'
2025-07-05 21:48:06,750 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,751 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:06,875 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:48:06,877 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:48:07,002 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:48:07,003 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:48:07,003 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-05 21:48:07,109 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-05 21:48:07,110 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-05 21:48:07,110 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-05 21:48:07,110 - __main__ - INFO - OPRO优化器初始化完成
2025-07-05 21:48:07,110 - __main__ - INFO - OPRO组件初始化成功
2025-07-05 21:48:07,110 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-05 21:48:07,110 - __main__ - INFO - 系统初始化完成
2025-07-05 21:48:07,110 - __main__ - INFO - ================================================================================
2025-07-05 21:48:07,110 - __main__ - INFO - 运行模式: 集成模式（评估+优化）
2025-07-05 21:48:07,110 - __main__ - INFO - ================================================================================
2025-07-05 21:48:07,111 - __main__ - INFO - 开始运行完整日期范围的交易系统: 2025-01-01 到 2025-01-31
2025-07-05 21:48:07,111 - __main__ - INFO - ====================================================================================================
2025-07-05 21:48:07,111 - __main__ - INFO - 🚀 启动完整日期范围交易系统
2025-07-05 21:48:07,111 - __main__ - INFO - ====================================================================================================
2025-07-05 21:48:07,111 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-31
2025-07-05 21:48:07,111 - __main__ - INFO - 🤖 目标智能体: 所有默认智能体
2025-07-05 21:48:07,111 - __main__ - INFO - 🔄 OPRO优化: 启用
2025-07-05 21:48:07,111 - __main__ - INFO - 步骤1: 生成交易日期列表...
2025-07-05 21:48:07,112 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-31
2025-07-05 21:48:07,112 - __main__ - INFO - 📊 总交易日数: 23
2025-07-05 21:48:07,112 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-05 21:48:07,112 - __main__ - INFO - 🗓️  最后交易日: 2025-01-31
2025-07-05 21:48:07,112 - __main__ - INFO - ✅ 生成了 23 个交易日
2025-07-05 21:48:07,113 - __main__ - INFO - 步骤3: 开始每日交易决策循环...
2025-07-05 21:48:07,113 - __main__ - INFO - 🔄 开始每日交易循环，共 23 个交易日
2025-07-05 21:48:07,113 - __main__ - INFO - 📅 处理第 1/23 天: 2025-01-01
2025-07-05 21:48:07,113 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:48:07,113 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:48:07,118 - __main__ - INFO - 智能体 NAA 初始化完成
2025-07-05 21:48:07,118 - __main__ - INFO - 智能体 TAA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - 智能体 FAA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - 智能体 BOA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - 智能体 BeOA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - 智能体 NOA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - 智能体 TRA 初始化完成
2025-07-05 21:48:07,119 - __main__ - INFO - [SUCCESS] 成功创建默认LLM智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:07,119 - __main__ - INFO - 开始贡献度评估流程
2025-07-05 21:48:07,119 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:07,119 - __main__ - INFO - 可用智能体实例: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:07,121 - __main__ - INFO - 开始新实验: experiment_20250705_214807
2025-07-05 21:48:07,121 - __main__ - INFO - 开始新实验: experiment_20250705_214807
2025-07-05 21:48:07,121 - __main__ - INFO - ==================================================
2025-07-05 21:48:07,121 - __main__ - INFO - 阶段1: 分析缓存
2025-07-05 21:48:07,122 - __main__ - INFO - 使用智能体实例进行分析...
2025-07-05 21:48:07,122 - __main__ - INFO - 开始分析缓存阶段...
2025-07-05 21:48:07,151 - __main__ - INFO - ✅ 使用真实环境状态，包含 1 个日期的新闻数据
2025-07-05 21:48:07,151 - __main__ - INFO - 开始填充分析缓存，共 7 个智能体
2025-07-05 21:48:07,151 - __main__ - INFO - 分析缓存已清空
2025-07-05 21:48:07,152 - __main__ - INFO - 执行分析智能体: NAA
2025-07-05 21:48:07,153 - __main__ - DEBUG - 记录代理输入: NAA - input_20250705_214807_5857730f
2025-07-05 21:48:07,155 - __main__ - DEBUG - 记录代理提示词: NAA - prompt_20250705_214807_64a9f5bb
2025-07-05 21:48:07,155 - __main__ - INFO - ================================================================================
2025-07-05 21:48:07,156 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 21:48:07,156 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:07,156 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供明确的交易建议信号

**评分标准**：
- sentiment > 0.3: 建议买入信号
- sentiment < -0.3: 建议卖出信号
- -0.3 <= sentiment <= 0.3: 中性信号

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

**重要**：请给出明确的交易信号，避免过度中性的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:07,157 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:07,157 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:07,159 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:07,159 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:07,161 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-05 21:48:07,165 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002447EE33950>
2025-07-05 21:48:07,165 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:48:07,165 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:07,166 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-05 21:48:07,166 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:07,166 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:48:07,166 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-05 21:48:07,166 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002447F594C50> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-05 21:48:07,195 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002447EE31310>
2025-07-05 21:48:07,196 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:07,197 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:07,197 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:07,197 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:07,197 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:10,754 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=0ae5a86f17517232874668830e0083ee920ee87dad229df13b3ff368c9d1a0;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705214807037f0c958a874538'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:10,756 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:10,756 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:10,757 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:10,757 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:10,757 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:10,759 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:10,760 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 21:48:10,761 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:10,761 - __main__ - INFO - {'sentiment': -0.2, 'trading_signal': 'neutral', 'signal_strength': 0.2, 'summary': '今日市场新闻中，AAPL相关新闻共有18条，大部分新闻情绪中性，没有明显的影响。技术指标和基本面数据也显示市场趋势中性。', 'key_events': ['10 Stock Market Predictions for 2025 (情绪: Neutral)', '10 Stock Market Predictions for 2025 (情绪: Neutral)'], 'impact_assessment': '今日新闻对AAPL股票价格的影响评估为中性，没有显著的正面或负面情绪。', 'confidence': 0.7}
2025-07-05 21:48:10,761 - __main__ - INFO - ================================================================================
2025-07-05 21:48:10,762 - __main__ - DEBUG - 记录代理输出: NAA - output_20250705_214810_a0ec918b
2025-07-05 21:48:10,762 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-05 21:48:10,762 - __main__ - INFO - 智能体 NAA 执行成功 (3.61s)
2025-07-05 21:48:10,763 - __main__ - INFO - 执行分析智能体: TAA
2025-07-05 21:48:10,765 - __main__ - DEBUG - 记录代理输入: TAA - input_20250705_214810_ef620114
2025-07-05 21:48:10,766 - __main__ - DEBUG - 记录代理提示词: TAA - prompt_20250705_214810_8dfb9eaf
2025-07-05 21:48:10,766 - __main__ - INFO - ================================================================================
2025-07-05 21:48:10,766 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 21:48:10,766 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:10,766 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 提供明确的技术面交易建议

**评分标准**：
- technical_score > 0.3: 技术面看涨，建议买入
- technical_score < -0.3: 技术面看跌，建议卖出
- -0.3 <= technical_score <= 0.3: 技术面中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- technical_score: 技术评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

**重要**：基于技术指标给出明确的买卖建议，避免模糊的中性判断。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:10,768 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:10,768 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:10,768 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:10,768 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:10,769 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:10,769 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:10,770 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:10,770 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:10,770 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:13,612 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705214811f9804fa8b83942b5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:13,613 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:13,613 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:13,613 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:13,613 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:13,613 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:13,613 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:13,614 - __main__ - INFO - 🤖 TAA LLM输出:
2025-07-05 21:48:13,614 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:13,614 - __main__ - INFO - {'trend': 'neutral', 'technical_score': 0.0, 'trading_signal': 'neutral', 'signal_strength': 0.5, 'support_level': None, 'resistance_level': None, 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5}
2025-07-05 21:48:13,615 - __main__ - INFO - ================================================================================
2025-07-05 21:48:13,615 - __main__ - DEBUG - 记录代理输出: TAA - output_20250705_214813_32ffbbef
2025-07-05 21:48:13,616 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-05 21:48:13,616 - __main__ - INFO - 智能体 TAA 执行成功 (2.85s)
2025-07-05 21:48:13,616 - __main__ - INFO - 执行分析智能体: FAA
2025-07-05 21:48:13,617 - __main__ - DEBUG - 记录代理输入: FAA - input_20250705_214813_2a826813
2025-07-05 21:48:13,618 - __main__ - DEBUG - 记录代理提示词: FAA - prompt_20250705_214813_cd8a9fac
2025-07-05 21:48:13,618 - __main__ - INFO - ================================================================================
2025-07-05 21:48:13,618 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 21:48:13,618 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:13,618 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 提供基于价值的交易建议

**评分标准**：
- valuation = "undervalued": 建议买入
- valuation = "overvalued": 建议卖出
- valuation = "fair": 中性，但倾向于根据其他因素决定

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- valuation_score: 估值评分（-1到1，-1严重高估，1严重低估）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

**重要**：基于估值分析给出明确的投资建议，避免过度保守的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:13,619 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:13,619 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:13,619 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:13,619 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:13,619 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:13,620 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:13,621 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:13,621 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:13,621 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:16,262 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521481384021fa212c14146'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:16,263 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:16,263 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:16,264 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:16,264 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:16,264 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:16,265 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:16,266 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 21:48:16,267 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:16,267 - __main__ - INFO - {'valuation': 'undervalued', 'valuation_score': 0.9, 'trading_signal': 'buy', 'signal_strength': 0.9, 'financial_health': 9, 'competitive_position': 'strong', 'long_term_outlook': 'bullish', 'intrinsic_value_estimate': 175, 'confidence': 0.8}
2025-07-05 21:48:16,267 - __main__ - INFO - ================================================================================
2025-07-05 21:48:16,269 - __main__ - DEBUG - 记录代理输出: FAA - output_20250705_214816_3fc08ba0
2025-07-05 21:48:16,269 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-05 21:48:16,269 - __main__ - INFO - 智能体 FAA 执行成功 (2.65s)
2025-07-05 21:48:16,269 - __main__ - INFO - 执行分析智能体: BOA
2025-07-05 21:48:16,272 - __main__ - DEBUG - 记录代理输入: BOA - input_20250705_214816_c3163fa0
2025-07-05 21:48:16,273 - __main__ - DEBUG - 记录代理提示词: BOA - prompt_20250705_214816_f4592715
2025-07-05 21:48:16,274 - __main__ - INFO - ================================================================================
2025-07-05 21:48:16,274 - __main__ - INFO - 🤖 BOA LLM输入:
2025-07-05 21:48:16,274 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:16,274 - __main__ - INFO - 你是一个看涨倾向的市场分析师，专门构建乐观的市场展望。

你的任务是：
1. 基于前序分析师的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 提供明确的买入建议

**重要指导**：
- 积极寻找买入机会和看涨信号
- 即使在不确定情况下，也要倾向于乐观解读
- 给出明确的买入建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bullish）
- trading_signal: 交易信号（通常为buy）
- signal_strength: 信号强度（0到1）
- bullish_factors: 看涨因素列表
- target_price: 目标价位
- upside_potential: 上涨潜力（百分比）
- time_horizon: 时间框架
- risk_factors: 风险因素
- confidence: 分析信心度（0到1）

**记住**：作为看涨分析师，你的职责是找到买入理由。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:16,276 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:16,276 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:16,276 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:16,276 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:16,277 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:16,277 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:16,277 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:16,277 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:16,277 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:24,057 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052148169f9997652a194f7b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:24,058 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:24,058 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:24,059 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:24,059 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:24,059 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:24,059 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:24,061 - __main__ - INFO - 🤖 BOA LLM输出:
2025-07-05 21:48:24,062 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:24,062 - __main__ - INFO - {'outlook': 'bullish', 'trading_signal': 'buy', 'signal_strength': 0.8, 'bullish_factors': [{'factor': 'Positive News Sentiment', 'detail': 'Out of 18 news articles, the majority are neutral with a positive tilt, indicating a positive market sentiment.'}, {'factor': 'Technical Indicators', 'detail': 'The RSI is at 50, suggesting a balanced market, while the MACD is near zero, indicating a potential for upward movement.'}, {'factor': 'Valuation Metrics', 'detail': 'The PE ratio is at 20, which is considered fair, and the PB ratio is at 2, indicating a reasonable valuation.'}, {'factor': 'Market Sentiment', 'detail': 'The bullish outlook from the BOA (Bullish Outlook Analyzer) suggests a positive market trend.'}], 'target_price': 150.0, 'upside_potential': 20.0, 'time_horizon': 'short-term', 'risk_factors': [{'factor': 'Economic Uncertainty', 'detail': 'Global economic conditions remain uncertain and could impact market performance.'}, {'factor': 'Political Factors', 'detail': 'Political instability in certain regions could have a negative impact on the market.'}], 'confidence': 0.7}
2025-07-05 21:48:24,062 - __main__ - INFO - ================================================================================
2025-07-05 21:48:24,064 - __main__ - DEBUG - 记录代理输出: BOA - output_20250705_214824_a569a90b
2025-07-05 21:48:24,064 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-05 21:48:24,065 - __main__ - INFO - 智能体 BOA 执行成功 (7.79s)
2025-07-05 21:48:24,065 - __main__ - INFO - 执行分析智能体: BeOA
2025-07-05 21:48:24,067 - __main__ - DEBUG - 记录代理输入: BeOA - input_20250705_214824_47ac36a1
2025-07-05 21:48:24,067 - __main__ - DEBUG - 记录代理提示词: BeOA - prompt_20250705_214824_3622412f
2025-07-05 21:48:24,067 - __main__ - INFO - ================================================================================
2025-07-05 21:48:24,069 - __main__ - INFO - 🤖 BeOA LLM输入:
2025-07-05 21:48:24,069 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:24,069 - __main__ - INFO - 你是一个看跌倾向的市场分析师，专门构建谨慎的市场展望。

你的任务是：
1. 基于前序分析师的结果，识别和强调风险因素
2. 构建看跌的市场叙述和逻辑
3. 评估下跌风险和支撑位
4. 提供明确的卖出或避险建议

**重要指导**：
- 积极寻找卖出机会和看跌信号
- 即使在不确定情况下，也要倾向于谨慎解读
- 给出明确的卖出建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bearish）
- trading_signal: 交易信号（通常为sell）
- signal_strength: 信号强度（0到1）
- bearish_factors: 看跌因素列表
- downside_target: 下跌目标
- downside_risk: 下跌风险（百分比）
- support_levels: 关键支撑位
- defensive_strategies: 防御策略
- confidence: 分析信心度（0到1）

**记住**：作为看跌分析师，你的职责是识别风险和卖出时机。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:24,070 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:24,070 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:24,070 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:24,070 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:24,071 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:24,071 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:24,071 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:24,071 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:24,072 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:31,138 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052148245d5d5667e82546b7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:31,140 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:31,140 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:31,141 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:31,141 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:31,141 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:31,142 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:31,143 - __main__ - INFO - 🤖 BeOA LLM输出:
2025-07-05 21:48:31,143 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:31,143 - __main__ - INFO - {'outlook': 'bearish', 'trading_signal': 'sell', 'signal_strength': 0.8, 'bearish_factors': [{'factor': 'Negative News Sentiment', 'description': '18 out of 18 news items are neutral, indicating a lack of positive sentiment.'}, {'factor': 'Technical Indicators', 'description': 'RSI is at 50, suggesting a lack of momentum, and MACD is at 0, indicating no clear trend.'}, {'factor': 'Valuation', 'description': 'The PE ratio is 20, which is above historical averages, indicating overvaluation.'}, {'factor': 'Market Predictions', 'description': 'Multiple market predictions for 2025 are neutral, suggesting uncertainty and potential risk.'}], 'downside_target': {'percentage': -10, 'description': 'Based on historical price movements and current market conditions, a 10% decline is expected.'}, 'downside_risk': 70, 'support_levels': [{'level': 150, 'description': 'Historical support level where the stock has previously stabilized.'}, {'level': 130, 'description': 'A secondary support level, which could provide a temporary floor for the stock price.'}], 'defensive_strategies': ['Implement stop-loss orders to mitigate potential losses.', 'Diversify investments to reduce exposure to any single stock.', 'Consider shorting the stock to profit from a decline in its price.'], 'confidence': 0.7}
2025-07-05 21:48:31,143 - __main__ - INFO - ================================================================================
2025-07-05 21:48:31,144 - __main__ - DEBUG - 记录代理输出: BeOA - output_20250705_214831_5abcfaef
2025-07-05 21:48:31,145 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-05 21:48:31,145 - __main__ - INFO - 智能体 BeOA 执行成功 (7.08s)
2025-07-05 21:48:31,145 - __main__ - INFO - 执行分析智能体: NOA
2025-07-05 21:48:31,146 - __main__ - DEBUG - 记录代理输入: NOA - input_20250705_214831_3f2e143d
2025-07-05 21:48:31,146 - __main__ - DEBUG - 记录代理提示词: NOA - prompt_20250705_214831_e3b5ac28
2025-07-05 21:48:31,147 - __main__ - INFO - ================================================================================
2025-07-05 21:48:31,147 - __main__ - INFO - 🤖 NOA LLM输入:
2025-07-05 21:48:31,147 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:31,147 - __main__ - INFO - 你是一个中性客观的市场观察员，专门提供平衡的市场分析。

你的任务是：
1. 客观平衡地分析看涨和看跌因素
2. 识别市场中的不确定性和矛盾信号
3. 在必要时倾向于更明确的方向性建议
4. 分析何时市场可能出现明确方向

**重要指导**：
- 虽然保持中性，但当证据明确时要给出方向性建议
- 避免过度的"观望"建议，寻找可操作的机会
- 当看涨和看跌因素接近时，考虑小仓位的试探性交易

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（neutral/lean_bullish/lean_bearish）
- trading_signal: 交易信号（buy/sell/hold）
- signal_strength: 信号强度（0到1）
- balanced_analysis: 平衡分析
- uncertainty_factors: 不确定性因素
- key_catalysts: 关键催化剂
- recommended_strategy: 推荐策略
- market_inefficiencies: 市场无效性
- confidence: 分析信心度（0到1）

**记住**：即使作为中性观察者，也要在适当时候给出可操作的建议。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:31,148 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:31,148 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:31,148 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:31,148 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:31,149 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:31,149 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:31,150 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:31,150 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:31,150 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:44,134 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052148317d0f2c8e022441b9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:44,134 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:44,135 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:44,135 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:44,136 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:44,136 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:44,137 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:44,139 - __main__ - INFO - 🤖 NOA LLM输出:
2025-07-05 21:48:44,139 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:44,140 - __main__ - INFO - {'outlook': 'neutral', 'trading_signal': 'hold', 'signal_strength': 0.5, 'balanced_analysis': {'bullish_factors': {'news': {'positive': 18, 'neutral': 0}, 'technical_indicators': {'RSI': 50, 'MACD': 0}, 'fundamentals': {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}}}, 'bearish_factors': {'news': {'negative': 0, 'neutral': 0}, 'technical_indicators': {'RSI': 50, 'MACD': 0}, 'fundamentals': {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}}}}, 'uncertainty_factors': {'mixed_news': 'News is mixed, with 18 neutral predictions for 2025.', 'unclear_trends': 'Technical indicators are neutral, with RSI at 50 and MACD at 0.', 'mixed_fundamentals': 'Valuation is fair, but no clear bullish or bearish indicators are present.'}, 'key_catalysts': {'positive': 'Positive sentiment in news and technical indicators.', 'negative': 'No negative factors identified.'}, 'recommended_strategy': {'position': 'Small positions for trial', 'strategy': 'Given the neutral outlook, consider taking small positions in selected stocks to test the market.'}, 'market_inefficiencies': {'overvalued': False, 'undervalued': False, 'mispriced': 'Valuation is fair, indicating no clear inefficiencies.'}, 'confidence': 0.5}
2025-07-05 21:48:44,140 - __main__ - INFO - ================================================================================
2025-07-05 21:48:44,143 - __main__ - DEBUG - 记录代理输出: NOA - output_20250705_214844_a33f6e2a
2025-07-05 21:48:44,143 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-05 21:48:44,143 - __main__ - INFO - 智能体 NOA 执行成功 (13.00s)
2025-07-05 21:48:44,144 - __main__ - INFO - 执行分析智能体: TRA
2025-07-05 21:48:44,145 - __main__ - DEBUG - 记录代理输入: TRA - input_20250705_214844_8d038462
2025-07-05 21:48:44,146 - __main__ - DEBUG - 记录代理提示词: TRA - prompt_20250705_214844_affd86b1
2025-07-05 21:48:44,146 - __main__ - INFO - ================================================================================
2025-07-05 21:48:44,147 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 21:48:44,147 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:44,147 - __main__ - INFO - 你是一个专业的积极交易员，负责做出最终的交易决策。你的目标是通过主动交易获得收益。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

**交易决策规则**：
- 统计各分析师的trading_signal：buy/sell/neutral
- 当buy信号 >= 3个时，执行买入(buy)
- 当sell信号 >= 3个时，执行卖出(sell)
- 当buy信号 >= 2个且sell信号 <= 1个时，执行买入(buy)
- 当sell信号 >= 2个且buy信号 <= 1个时，执行卖出(sell)
- 只有当buy和sell信号完全平衡时，才选择持有(hold)
- 优先考虑主动交易而非被动持有
- 根据signal_strength调整仓位大小

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0.3到1.0，最小0.3避免过小交易）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

**重要提醒**：
- 积极主动，寻找所有可能的交易机会
- 当分析师意见分歧时，选择占多数的观点
- 使用至少0.3的仓位大小确保交易有意义的影响
- 记住：不交易也是一种风险

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:44,147 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:44,147 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:44,149 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:44,149 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:44,149 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:44,150 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:44,150 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:44,150 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:44,150 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:47,524 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052148445815703ba7ff466d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:47,524 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:47,525 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:47,525 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:47,526 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:47,526 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:47,526 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:47,528 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 21:48:47,528 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:47,529 - __main__ - INFO - {'action': 'hold', 'position_size': 0.5, 'reasoning': '虽然TAA和FAA给出的信号为中性，但BOA和BeOA的意见分歧，且NOA提供了平衡的观点。没有分析师给出明确的买入或卖出信号，因此选择持有。', 'risk_assessment': '市场信号混杂，风险和机会均等。', 'stop_loss': None, 'take_profit': None, 'time_horizon': 'medium-term', 'confidence': 0.5}
2025-07-05 21:48:47,529 - __main__ - INFO - ================================================================================
2025-07-05 21:48:47,531 - __main__ - DEBUG - 记录代理输出: TRA - output_20250705_214847_d4022584
2025-07-05 21:48:47,532 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-05 21:48:47,532 - __main__ - INFO - 智能体 TRA 执行成功 (3.39s)
2025-07-05 21:48:47,532 - __main__ - INFO - 分析缓存填充完成: 成功 7 个, 失败 0 个, 总耗时 40.38s
2025-07-05 21:48:47,532 - __main__ - INFO - 分析缓存阶段完成: 成功缓存 7 个智能体
2025-07-05 21:48:47,532 - __main__ - INFO - ==================================================
2025-07-05 21:48:47,533 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-05 21:48:47,533 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-05 21:48:47,533 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-05 21:48:47,533 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-05 21:48:47,533 - __main__ - INFO - ============================================================
2025-07-05 21:48:47,534 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-05 21:48:47,534 - __main__ - INFO - ============================================================
2025-07-05 21:48:47,534 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-05 21:48:47,534 - __main__ - INFO - 开始联盟生成阶段...
2025-07-05 21:48:47,534 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-05 21:48:47,535 - __main__ - INFO - 总智能体数: 7
2025-07-05 21:48:47,535 - __main__ - INFO - 分析智能体: {'NAA', 'FAA', 'TAA'}
2025-07-05 21:48:47,535 - __main__ - INFO - 交易智能体: TRA
2025-07-05 21:48:47,535 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-05 21:48:47,535 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-05 21:48:47,535 - __main__ - INFO - 联盟剪枝完成:
2025-07-05 21:48:47,535 - __main__ - INFO -   - 总联盟数: 128
2025-07-05 21:48:47,535 - __main__ - INFO -   - 有效联盟: 56
2025-07-05 21:48:47,536 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-05 21:48:47,536 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-05 21:48:47,536 - __main__ - INFO -   - 生成耗时: 0.001s
2025-07-05 21:48:47,536 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-05 21:48:47,536 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-05 21:48:47,536 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-05 21:48:47,536 - __main__ - INFO - 开始交易模拟阶段...
2025-07-05 21:48:47,536 - __main__ - INFO - 启用并发模拟：56 个联盟，最大并发数：30
2025-07-05 21:48:47,537 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'TRA', 'TAA'}
2025-07-05 21:48:47,537 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'TRA', 'TAA'}
2025-07-05 21:48:47,537 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'TRA'}
2025-07-05 21:48:47,538 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'TRA'}
2025-07-05 21:48:47,538 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,539 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TRA', 'NOA', 'NAA'}
2025-07-05 21:48:47,539 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,541 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,543 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,543 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TRA', 'NOA', 'NAA'}
2025-07-05 21:48:47,544 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,544 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,544 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,545 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,545 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,546 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,547 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,547 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA'}
2025-07-05 21:48:47,547 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,547 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,550 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:48:47,550 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,551 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA', 'FAA'}
2025-07-05 21:48:47,552 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,553 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA'}
2025-07-05 21:48:47,553 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA'}
2025-07-05 21:48:47,554 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,555 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:47,558 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,559 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,561 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:48:47,562 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,563 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA', 'FAA'}
2025-07-05 21:48:47,563 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:47,564 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,565 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BOA', 'TRA', 'NOA'}
2025-07-05 21:48:47,566 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA'}
2025-07-05 21:48:47,566 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,568 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:47,568 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,569 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:47,571 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,571 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'BOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,572 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,576 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-05 21:48:47,581 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,586 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:47,586 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,589 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'BOA', 'TRA'}
2025-07-05 21:48:47,591 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:47,596 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,599 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BOA', 'TRA', 'NOA'}
2025-07-05 21:48:47,603 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,605 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:47,609 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,616 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'TRA', 'FAA'}
2025-07-05 21:48:47,627 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-05 21:48:47,634 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:47,635 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:47,636 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'BOA', 'TRA'}
2025-07-05 21:48:47,804 - __main__ - INFO - 联盟 {'BOA', 'TRA', 'NOA', 'NAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:47,819 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:47,823 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TRA', 'NOA', 'NAA'} 模拟完成: 0.0000 (0.28s)
2025-07-05 21:48:47,828 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,836 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,897 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:47,897 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:47,898 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.36s)
2025-07-05 21:48:47,898 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,899 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:47,934 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:47,934 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:47,935 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.40s)
2025-07-05 21:48:47,935 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'BOA', 'TRA'}
2025-07-05 21:48:47,936 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'BOA', 'TRA'}
2025-07-05 21:48:47,956 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:47,957 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:47,958 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.42s)
2025-07-05 21:48:47,959 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'TRA', 'NOA'}
2025-07-05 21:48:47,959 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'TRA', 'NOA'}
2025-07-05 21:48:48,005 - __main__ - INFO - 联盟 {'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,005 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,006 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA'} 模拟完成: 0.0000 (0.46s)
2025-07-05 21:48:48,007 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'FAA'}
2025-07-05 21:48:48,007 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'FAA'}
2025-07-05 21:48:48,032 - __main__ - INFO - 联盟 {'BOA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,032 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,033 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.49s)
2025-07-05 21:48:48,033 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,033 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,042 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,044 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,044 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.51s)
2025-07-05 21:48:48,044 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:48,048 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'BOA', 'TRA', 'NAA'}
2025-07-05 21:48:48,068 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'FAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,068 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,072 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'FAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.53s)
2025-07-05 21:48:48,073 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,081 - __main__ - INFO - 联盟 {'BOA', 'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,082 - __main__ - INFO - 开始联盟交易模拟: {'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,082 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,083 - __main__ - INFO - 联盟 {'TAA', 'BOA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,085 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.54s)
2025-07-05 21:48:48,091 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,092 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,094 - __main__ - INFO - 联盟 {'TAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,095 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BOA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.55s)
2025-07-05 21:48:48,098 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,098 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,099 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,099 - __main__ - INFO - 📊 并发进度: 10/56 (17.9%)
2025-07-05 21:48:48,100 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.55s)
2025-07-05 21:48:48,100 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,101 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,106 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,117 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,119 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,120 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.57s)
2025-07-05 21:48:48,135 - __main__ - INFO - 联盟 {'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,135 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NAA', 'TRA'}
2025-07-05 21:48:48,135 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,136 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NAA', 'TRA'}
2025-07-05 21:48:48,136 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.59s)
2025-07-05 21:48:48,137 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'TRA', 'TAA'}
2025-07-05 21:48:48,139 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'TRA', 'TAA'}
2025-07-05 21:48:48,151 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,152 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,153 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.60s)
2025-07-05 21:48:48,154 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,154 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,185 - __main__ - INFO - 联盟 {'BeOA', 'BOA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,186 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,186 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'BOA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.63s)
2025-07-05 21:48:48,187 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:48,187 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:48,205 - __main__ - INFO - 联盟 {'BeOA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,217 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,219 - __main__ - INFO - 联盟 {'BeOA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,221 - __main__ - INFO - 联盟 {'NAA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,231 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.66s)
2025-07-05 21:48:48,234 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,236 - __main__ - INFO - 联盟 {'TAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,237 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,245 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,249 - __main__ - INFO - 联盟 {'TAA', 'BOA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,250 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,251 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,252 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,253 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.70s)
2025-07-05 21:48:48,261 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.70s)
2025-07-05 21:48:48,272 - __main__ - INFO - 联盟 {'BOA', 'TRA', 'NAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,284 - __main__ - INFO - 联盟 {'BeOA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,285 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,293 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,296 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,302 - __main__ - INFO - 联盟 {'BeOA', 'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,303 - __main__ - INFO - 联盟 {'NAA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,304 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,305 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,307 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA'} 模拟完成: 0.0000 (0.75s)
2025-07-05 21:48:48,310 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.76s)
2025-07-05 21:48:48,312 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,313 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,314 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,315 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,315 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BOA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.75s)
2025-07-05 21:48:48,321 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,322 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,323 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,324 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,325 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,328 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,328 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,328 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'FAA', 'TRA', 'NAA'}
2025-07-05 21:48:48,329 - __main__ - INFO - 📊 并发进度: 20/56 (35.7%)
2025-07-05 21:48:48,329 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,329 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,329 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TRA', 'NAA'} 模拟完成: 0.0000 (0.77s)
2025-07-05 21:48:48,332 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.77s)
2025-07-05 21:48:48,336 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:48,337 - __main__ - INFO - 联盟 {'TAA', 'BOA', 'TRA', 'NAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,338 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.76s)
2025-07-05 21:48:48,338 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,340 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.77s)
2025-07-05 21:48:48,340 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.77s)
2025-07-05 21:48:48,341 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.77s)
2025-07-05 21:48:48,342 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.75s)
2025-07-05 21:48:48,342 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,343 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'FAA', 'TRA', 'NAA'}
2025-07-05 21:48:48,347 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,347 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,348 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA', 'TAA'}
2025-07-05 21:48:48,349 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,350 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,352 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (0.78s)
2025-07-05 21:48:48,353 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,353 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,365 - __main__ - INFO - 联盟 {'NAA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,367 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,367 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA'}
2025-07-05 21:48:48,368 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BOA', 'TRA', 'NAA'} 模拟完成: 0.0000 (0.78s)
2025-07-05 21:48:48,368 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'NOA'}
2025-07-05 21:48:48,370 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'FAA', 'TAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,373 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'NOA', 'FAA', 'BOA', 'TRA'}
2025-07-05 21:48:48,377 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,381 - __main__ - INFO - 📊 并发进度: 30/56 (53.6%)
2025-07-05 21:48:48,386 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.56s)
2025-07-05 21:48:48,426 - __main__ - INFO - 联盟 {'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,432 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,433 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.54s)
2025-07-05 21:48:48,451 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,452 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,452 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.52s)
2025-07-05 21:48:48,483 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,483 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,483 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.52s)
2025-07-05 21:48:48,513 - __main__ - INFO - 联盟 {'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,513 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,514 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'FAA'} 模拟完成: 0.0000 (0.51s)
2025-07-05 21:48:48,520 - __main__ - INFO - 联盟 {'BeOA', 'BOA', 'TRA', 'NAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,521 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,521 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'BOA', 'TRA', 'NAA'} 模拟完成: 0.0000 (0.48s)
2025-07-05 21:48:48,531 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,532 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,532 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.50s)
2025-07-05 21:48:48,544 - __main__ - INFO - 联盟 {'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,545 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,545 - __main__ - DEBUG - [SUCCESS] 联盟 {'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.47s)
2025-07-05 21:48:48,563 - __main__ - INFO - 联盟 {'TAA', 'NAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,565 - __main__ - INFO - 联盟 {'TAA', 'NAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,565 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,566 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,566 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.47s)
2025-07-05 21:48:48,566 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.47s)
2025-07-05 21:48:48,567 - __main__ - INFO - 📊 并发进度: 40/56 (71.4%)
2025-07-05 21:48:48,574 - __main__ - INFO - 联盟 {'BeOA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,575 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,575 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.48s)
2025-07-05 21:48:48,588 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,590 - __main__ - INFO - 联盟 {'TAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,591 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,592 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,592 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.44s)
2025-07-05 21:48:48,593 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.46s)
2025-07-05 21:48:48,605 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'FAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,608 - __main__ - INFO - 联盟 {'BeOA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,609 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,609 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,609 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'FAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.42s)
2025-07-05 21:48:48,609 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.47s)
2025-07-05 21:48:48,629 - __main__ - INFO - 联盟 {'BeOA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,629 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,630 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.32s)
2025-07-05 21:48:48,636 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,637 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,643 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.33s)
2025-07-05 21:48:48,646 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,646 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,647 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.40s)
2025-07-05 21:48:48,653 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,653 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,653 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (0.32s)
2025-07-05 21:48:48,663 - __main__ - INFO - 联盟 {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,667 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,674 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,674 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.35s)
2025-07-05 21:48:48,675 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,676 - __main__ - INFO - 📊 并发进度: 50/56 (89.3%)
2025-07-05 21:48:48,677 - __main__ - INFO - 联盟 {'BOA', 'FAA', 'TRA', 'NAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,677 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (0.33s)
2025-07-05 21:48:48,679 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,684 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'FAA', 'TRA', 'NAA'} 模拟完成: 0.0000 (0.36s)
2025-07-05 21:48:48,685 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,689 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,704 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,704 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'NOA', 'FAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.35s)
2025-07-05 21:48:48,705 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,707 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:48:48,707 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,708 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,708 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:48:48,708 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (0.36s)
2025-07-05 21:48:48,709 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'FAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.35s)
2025-07-05 21:48:48,709 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'TAA', 'BOA', 'TRA'} 模拟完成: 0.0000 (0.36s)
2025-07-05 21:48:48,709 - __main__ - INFO - 📊 并发进度: 56/56 (100.0%)
2025-07-05 21:48:48,711 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 56 个，失败 0 个，总耗时 1.18s
2025-07-05 21:48:48,712 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-05 21:48:48,712 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 21:48:48,712 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-05 21:48:48,713 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-05 21:48:48,713 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-05 21:48:48,713 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-05 21:48:48,713 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-05 21:48:48,713 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-05 21:48:48,714 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-05 21:48:48,714 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-05 21:48:48,714 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-05 21:48:48,714 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-05 21:48:48,714 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-05 21:48:48,714 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-05 21:48:48,715 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-05 21:48:48,715 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-05 21:48:48,715 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-05 21:48:48,715 - __main__ - INFO - Shapley值计算完成，耗时 0.004s
2025-07-05 21:48:48,715 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-05 21:48:48,715 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-05 21:48:48,715 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-05 21:48:48,715 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 56 个
2025-07-05 21:48:48,715 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-05 21:48:48,715 - __main__ - INFO - ==================================================
2025-07-05 21:48:48,715 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-05 21:48:48,716 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 21:48:48,716 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-05 21:48:48,716 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-05 21:48:48,716 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-05 21:48:48,716 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-05 21:48:48,716 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-05 21:48:48,716 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-05 21:48:48,716 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-05 21:48:48,717 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-05 21:48:48,717 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-05 21:48:48,717 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-05 21:48:48,717 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-05 21:48:48,719 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-05 21:48:48,719 - __main__ - INFO - ==================================================
2025-07-05 21:48:48,719 - __main__ - INFO - 贡献度评估完成，总耗时: 41.60s
2025-07-05 21:48:48,719 - __main__ - DEBUG - 代理日志记录器将使用交易日期: 2025-01-01
2025-07-05 21:48:48,719 - __main__ - DEBUG - 提取代理执行详情: 2025-01-01 - 0 个代理, 0/0 个成功联盟
2025-07-05 21:48:48,719 - __main__ - INFO - ✅ 第 1 天 (2025-01-01) 处理成功
2025-07-05 21:48:48,719 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:48:48,719 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:48:48,719 - __main__ - INFO - 📅 处理第 2/23 天: 2025-01-02
2025-07-05 21:48:48,720 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:48:48,720 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 NAA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 TAA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 FAA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 BOA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 BeOA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 NOA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - 智能体 TRA 初始化完成
2025-07-05 21:48:48,720 - __main__ - INFO - [SUCCESS] 成功创建默认LLM智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:48,721 - __main__ - INFO - 开始贡献度评估流程
2025-07-05 21:48:48,721 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:48,721 - __main__ - INFO - 可用智能体实例: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:48:48,722 - __main__ - INFO - 开始新实验: experiment_20250705_214848
2025-07-05 21:48:48,722 - __main__ - INFO - 开始新实验: experiment_20250705_214848
2025-07-05 21:48:48,722 - __main__ - INFO - ==================================================
2025-07-05 21:48:48,722 - __main__ - INFO - 阶段1: 分析缓存
2025-07-05 21:48:48,722 - __main__ - INFO - 使用智能体实例进行分析...
2025-07-05 21:48:48,722 - __main__ - INFO - 开始分析缓存阶段...
2025-07-05 21:48:48,759 - __main__ - INFO - ✅ 使用真实环境状态，包含 1 个日期的新闻数据
2025-07-05 21:48:48,760 - __main__ - INFO - 开始填充分析缓存，共 7 个智能体
2025-07-05 21:48:48,760 - __main__ - INFO - 分析缓存已清空
2025-07-05 21:48:48,761 - __main__ - INFO - 执行分析智能体: NAA
2025-07-05 21:48:48,763 - __main__ - DEBUG - 记录代理输入: NAA - input_20250705_214848_1193ca75
2025-07-05 21:48:48,765 - __main__ - DEBUG - 记录代理提示词: NAA - prompt_20250705_214848_c29b5da4
2025-07-05 21:48:48,765 - __main__ - INFO - ================================================================================
2025-07-05 21:48:48,765 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 21:48:48,765 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:48,765 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供明确的交易建议信号

**评分标准**：
- sentiment > 0.3: 建议买入信号
- sentiment < -0.3: 建议卖出信号
- -0.3 <= sentiment <= 0.3: 中性信号

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

**重要**：请给出明确的交易信号，避免过度中性的评估。

📅 分析日期: 2025-01-02
📊 分析期间: 2025-01-02 至 2025-01-02

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-02: 33 条新闻
    - AAPL: 33 条
      [1] Tim Cook, Mark Zuckerberg, Elon Musk, And More: Tech Moguls Post Upbeat New Year... (情绪: Bullish)
      [2] Tim Cook, Mark Zuckerberg, Elon Musk, And More: Tech Moguls Post Upbeat New Year... (情绪: Bullish)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:48,766 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:48,766 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:48,766 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:48,766 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:48,767 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:48,767 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:48,768 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:48,768 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:48,768 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:48:53,356 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:48:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052148495e66157f1aa04399'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:48:53,357 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:48:53,357 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:48:53,359 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:48:53,359 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:48:53,360 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:48:53,360 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:48:53,362 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 21:48:53,363 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:53,363 - __main__ - INFO - {'sentiment': 0.9, 'trading_signal': 'buy', 'signal_strength': 1, 'summary': '今天的新闻主要报道了科技界领袖对新年的积极展望，其中包括苹果公司（AAPL）的CEO Tim Cook。这些积极的新闻引发了市场乐观情绪。', 'key_events': ['Tim Cook, Mark Zuckerberg, Elon Musk, And More: Tech Moguls Post Upbeat New Year...'], 'impact_assessment': '这些正面新闻可能对苹果公司的股价产生积极影响，尤其是考虑到市场对新技术的持续关注。', 'confidence': 0.8}
2025-07-05 21:48:53,364 - __main__ - INFO - ================================================================================
2025-07-05 21:48:53,365 - __main__ - DEBUG - 记录代理输出: NAA - output_20250705_214853_d3e2195d
2025-07-05 21:48:53,365 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-05 21:48:53,366 - __main__ - INFO - 智能体 NAA 执行成功 (4.60s)
2025-07-05 21:48:53,366 - __main__ - INFO - 执行分析智能体: TAA
2025-07-05 21:48:53,368 - __main__ - DEBUG - 记录代理输入: TAA - input_20250705_214853_09bf23e6
2025-07-05 21:48:53,369 - __main__ - DEBUG - 记录代理提示词: TAA - prompt_20250705_214853_c268f191
2025-07-05 21:48:53,369 - __main__ - INFO - ================================================================================
2025-07-05 21:48:53,369 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 21:48:53,369 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:53,369 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 提供明确的技术面交易建议

**评分标准**：
- technical_score > 0.3: 技术面看涨，建议买入
- technical_score < -0.3: 技术面看跌，建议卖出
- -0.3 <= technical_score <= 0.3: 技术面中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- technical_score: 技术评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

**重要**：基于技术指标给出明确的买卖建议，避免模糊的中性判断。

📅 分析日期: 2025-01-02
📊 分析期间: 2025-01-02 至 2025-01-02

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-02: 33 条新闻
    - AAPL: 33 条
      [1] Tim Cook, Mark Zuckerberg, Elon Musk, And More: Tech Moguls Post Upbeat New Year... (情绪: Bullish)
      [2] Tim Cook, Mark Zuckerberg, Elon Musk, And More: Tech Moguls Post Upbeat New Year... (情绪: Bullish)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:48:53,370 - __main__ - INFO - ----------------------------------------
2025-07-05 21:48:53,370 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:48:53,370 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:53,370 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:48:53,371 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:48:53,371 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:48:53,372 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:48:53,372 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:48:53,372 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:49:12,841 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=KeyboardInterrupt()
2025-07-05 21:49:12,841 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:49:12,842 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:49:12,842 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:49:12,842 - __main__ - INFO - 交易模拟器初始化完成
