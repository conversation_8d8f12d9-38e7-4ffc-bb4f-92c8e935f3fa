2025-07-05 21:32:26,967 - __main__ - INFO - ====================================================================================================
2025-07-05 21:32:26,967 - __main__ - INFO - OPRO系统启动
2025-07-05 21:32:26,968 - __main__ - INFO - ====================================================================================================
2025-07-05 21:32:26,968 - __main__ - INFO - 运行模式: integrated
2025-07-05 21:32:26,968 - __main__ - INFO - LLM提供商: zhipuai
2025-07-05 21:32:26,968 - __main__ - INFO - OPRO启用: True
2025-07-05 21:32:26,968 - __main__ - INFO - 数据存储启用: True
2025-07-05 21:32:26,968 - __main__ - INFO - 代理日志记录启用: True
2025-07-05 21:32:26,969 - __main__ - INFO - 初始化系统...
2025-07-05 21:32:26,969 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:32:26,970 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:26,971 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,089 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:27,090 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,205 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:27,206 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,321 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:32:27,322 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:32:27,322 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-05 21:32:27,323 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:32:27,323 - __main__ - INFO - 设置实验日期: 2025-07-05
2025-07-05 21:32:27,323 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-05)
2025-07-05 21:32:27,323 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:32:27,324 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:32:27,324 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:32:27,324 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:32:27,324 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:32:27,324 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:32:27,332 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:32:27,333 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:32:27,334 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:32:27,334 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-05 21:32:27,334 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-05 21:32:27,335 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:32:27,335 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:32:27,335 - __main__ - INFO - 可视化管理器初始化完成
2025-07-05 21:32:27,340 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-05 21:32:27,341 - __main__ - INFO - A/B测试框架初始化完成
2025-07-05 21:32:27,341 - __main__ - INFO - 数据分析工具初始化完成
2025-07-05 21:32:27,342 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-05 21:32:27,342 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-05 21:32:27,342 - __main__ - INFO - 备份管理器初始化完成
2025-07-05 21:32:27,343 - __main__ - INFO - 数据备份完成: backup_20250705_213227 (0.02 MB)
2025-07-05 21:32:27,343 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-05 21:32:27,343 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-05 21:32:27,343 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-05 21:32:27,343 - __main__ - INFO - 分析缓存初始化完成
2025-07-05 21:32:27,343 - __main__ - INFO - 联盟管理器初始化完成
2025-07-05 21:32:27,343 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:32:27,343 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:32:27,343 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:32:27,344 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:32:27,345 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:32:27,345 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:32:27,345 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:32:27,346 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:32:27,347 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:32:27,347 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-05 21:32:27,347 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:32:27,347 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:27,349 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,352 - __main__ - INFO - 数据备份完成: backup_20250705_213227 (0.02 MB)
2025-07-05 21:32:27,469 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:27,470 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,589 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:32:27,589 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:32:27,712 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:32:27,714 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:32:27,714 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-05 21:32:27,826 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-05 21:32:27,826 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-05 21:32:27,826 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-05 21:32:27,826 - __main__ - INFO - OPRO优化器初始化完成
2025-07-05 21:32:27,826 - __main__ - INFO - OPRO组件初始化成功
2025-07-05 21:32:27,826 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-05 21:32:27,826 - __main__ - INFO - 系统初始化完成
2025-07-05 21:32:27,826 - __main__ - INFO - ================================================================================
2025-07-05 21:32:27,827 - __main__ - INFO - 运行模式: 集成模式（评估+优化）
2025-07-05 21:32:27,827 - __main__ - INFO - ================================================================================
2025-07-05 21:32:27,827 - __main__ - INFO - 开始运行完整日期范围的交易系统: 2025-01-01 到 2025-01-31
2025-07-05 21:32:27,827 - __main__ - INFO - ====================================================================================================
2025-07-05 21:32:27,827 - __main__ - INFO - 🚀 启动完整日期范围交易系统
2025-07-05 21:32:27,827 - __main__ - INFO - ====================================================================================================
2025-07-05 21:32:27,827 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-31
2025-07-05 21:32:27,827 - __main__ - INFO - 🤖 目标智能体: 所有默认智能体
2025-07-05 21:32:27,827 - __main__ - INFO - 🔄 OPRO优化: 启用
2025-07-05 21:32:27,827 - __main__ - INFO - 步骤1: 生成交易日期列表...
2025-07-05 21:32:27,828 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-31
2025-07-05 21:32:27,828 - __main__ - INFO - 📊 总交易日数: 23
2025-07-05 21:32:27,828 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-05 21:32:27,828 - __main__ - INFO - 🗓️  最后交易日: 2025-01-31
2025-07-05 21:32:27,828 - __main__ - INFO - ✅ 生成了 23 个交易日
2025-07-05 21:32:27,828 - __main__ - INFO - 步骤3: 开始每日交易决策循环...
2025-07-05 21:32:27,828 - __main__ - INFO - 🔄 开始每日交易循环，共 23 个交易日
2025-07-05 21:32:27,828 - __main__ - INFO - 📅 处理第 1/23 天: 2025-01-01
2025-07-05 21:32:27,828 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:32:27,829 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:32:27,833 - __main__ - INFO - 智能体 NAA 初始化完成
2025-07-05 21:32:27,833 - __main__ - INFO - 智能体 TAA 初始化完成
2025-07-05 21:32:27,833 - __main__ - INFO - 智能体 FAA 初始化完成
2025-07-05 21:32:27,834 - __main__ - INFO - 智能体 BOA 初始化完成
2025-07-05 21:32:27,834 - __main__ - INFO - 智能体 BeOA 初始化完成
2025-07-05 21:32:27,834 - __main__ - INFO - 智能体 NOA 初始化完成
2025-07-05 21:32:27,834 - __main__ - INFO - 智能体 TRA 初始化完成
2025-07-05 21:32:27,834 - __main__ - INFO - [SUCCESS] 成功创建默认LLM智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:32:27,834 - __main__ - INFO - 开始贡献度评估流程
2025-07-05 21:32:27,834 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:32:27,834 - __main__ - INFO - 可用智能体实例: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:32:27,835 - __main__ - INFO - 开始新实验: experiment_20250705_213227
2025-07-05 21:32:27,835 - __main__ - INFO - 开始新实验: experiment_20250705_213227
2025-07-05 21:32:27,836 - __main__ - INFO - ==================================================
2025-07-05 21:32:27,836 - __main__ - INFO - 阶段1: 分析缓存
2025-07-05 21:32:27,836 - __main__ - INFO - 使用智能体实例进行分析...
2025-07-05 21:32:27,836 - __main__ - INFO - 开始分析缓存阶段...
2025-07-05 21:32:27,836 - __main__ - INFO - 开始填充分析缓存，共 7 个智能体
2025-07-05 21:32:27,837 - __main__ - INFO - 分析缓存已清空
2025-07-05 21:32:27,837 - __main__ - INFO - 执行分析智能体: NAA
2025-07-05 21:32:27,838 - __main__ - DEBUG - 记录代理输入: NAA - input_20250705_213227_83b5adbd
2025-07-05 21:32:27,841 - __main__ - DEBUG - 记录代理提示词: NAA - prompt_20250705_213227_7a1e8017
2025-07-05 21:32:27,841 - __main__ - INFO - ================================================================================
2025-07-05 21:32:27,841 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 21:32:27,841 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:27,841 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供明确的交易建议信号

**评分标准**：
- sentiment > 0.3: 建议买入信号
- sentiment < -0.3: 建议卖出信号
- -0.3 <= sentiment <= 0.3: 中性信号

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

**重要**：请给出明确的交易信号，避免过度中性的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📰 新闻历史信息:
  • 暂无新闻数据
💵 可用现金: $1,000,000.00

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:32:27,842 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:27,842 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:32:27,843 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:27,843 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:27,845 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-05 21:32:27,848 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001ABFEC91E20>
2025-07-05 21:32:27,849 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:32:27,849 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:32:27,850 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-05 21:32:27,851 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:32:27,851 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:32:27,851 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-05 21:32:27,851 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001ABFFC419D0> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-05 21:32:27,909 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001ABFF4DF9E0>
2025-07-05 21:32:27,910 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:32:27,910 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:32:27,911 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:32:27,911 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:32:27,911 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:32:30,942 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:32:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=0ae52d1d17517223481752292e0054c81ba5cf1b2acfe06af9958efa7cf0c1;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052132286df61d89f72642d3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:32:30,943 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:32:30,944 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:32:30,945 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:32:30,945 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:32:30,946 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:32:30,946 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:32:30,952 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 21:32:30,952 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:30,952 - __main__ - INFO - {'sentiment': -0.2, 'trading_signal': 'neutral', 'signal_strength': 0.2, 'summary': '今日市场无重大新闻，分析师数据均为模拟数据，市场展望亦为模拟。技术指标和估值指标均显示中性趋势。', 'key_events': ['无新闻数据可用', '模拟技术分析数据', '模拟基本面分析数据'], 'impact_assessment': '由于缺乏实际新闻数据，无法准确评估市场情绪对股票价格的影响。', 'confidence': 0.5}
2025-07-05 21:32:30,952 - __main__ - INFO - ================================================================================
2025-07-05 21:32:30,953 - __main__ - DEBUG - 记录代理输出: NAA - output_20250705_213230_29ad5cf7
2025-07-05 21:32:30,953 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-05 21:32:30,954 - __main__ - INFO - 智能体 NAA 执行成功 (3.12s)
2025-07-05 21:32:30,954 - __main__ - INFO - 执行分析智能体: TAA
2025-07-05 21:32:30,956 - __main__ - DEBUG - 记录代理输入: TAA - input_20250705_213230_347200e6
2025-07-05 21:32:30,958 - __main__ - DEBUG - 记录代理提示词: TAA - prompt_20250705_213230_f93e3ac6
2025-07-05 21:32:30,958 - __main__ - INFO - ================================================================================
2025-07-05 21:32:30,958 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 21:32:30,958 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:30,958 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 提供明确的技术面交易建议

**评分标准**：
- technical_score > 0.3: 技术面看涨，建议买入
- technical_score < -0.3: 技术面看跌，建议卖出
- -0.3 <= technical_score <= 0.3: 技术面中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- technical_score: 技术评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

**重要**：基于技术指标给出明确的买卖建议，避免模糊的中性判断。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📰 新闻历史信息:
  • 暂无新闻数据
💵 可用现金: $1,000,000.00

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:32:30,959 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:30,959 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:32:30,959 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:30,959 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:30,960 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:32:30,961 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:32:30,961 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:32:30,961 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:32:30,962 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:32:41,227 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:32:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052132312929264a22d44552'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:32:41,227 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:32:41,229 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:32:41,229 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:32:41,230 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:32:41,230 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:32:41,230 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:32:41,232 - __main__ - INFO - 🤖 TAA LLM输出:
2025-07-05 21:32:41,232 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:41,233 - __main__ - INFO - {'trend': 'neutral', 'technical_score': 0.0, 'trading_signal': 'neutral', 'signal_strength': 0.0, 'support_level': None, 'resistance_level': None, 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'analysis_details': {'NAA': {'confidence': 0.5}, 'TAA': {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5}, 'FAA': {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5}, 'BOA': {'bullish': True, 'confidence': 0.6}, 'BeOA': {'bearish': True, 'confidence': 0.6}, 'NOA': {'neutral': True, 'confidence': 0.6}}}
2025-07-05 21:32:41,233 - __main__ - INFO - ================================================================================
2025-07-05 21:32:41,234 - __main__ - DEBUG - 记录代理输出: TAA - output_20250705_213241_454c8786
2025-07-05 21:32:41,236 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-05 21:32:41,236 - __main__ - INFO - 智能体 TAA 执行成功 (10.28s)
2025-07-05 21:32:41,236 - __main__ - INFO - 执行分析智能体: FAA
2025-07-05 21:32:41,237 - __main__ - DEBUG - 记录代理输入: FAA - input_20250705_213241_7862f226
2025-07-05 21:32:41,238 - __main__ - DEBUG - 记录代理提示词: FAA - prompt_20250705_213241_034aa036
2025-07-05 21:32:41,238 - __main__ - INFO - ================================================================================
2025-07-05 21:32:41,238 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 21:32:41,238 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:41,238 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 提供基于价值的交易建议

**评分标准**：
- valuation = "undervalued": 建议买入
- valuation = "overvalued": 建议卖出
- valuation = "fair": 中性，但倾向于根据其他因素决定

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- valuation_score: 估值评分（-1到1，-1严重高估，1严重低估）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

**重要**：基于估值分析给出明确的投资建议，避免过度保守的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📰 新闻历史信息:
  • 暂无新闻数据
💵 可用现金: $1,000,000.00

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:32:41,240 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:41,241 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:32:41,241 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:41,241 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:41,242 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:32:41,242 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:32:41,243 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:32:41,244 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:32:41,244 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:32:44,942 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:32:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521324191fb6172dd88413d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:32:44,943 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:32:44,943 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:32:44,944 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:32:44,944 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:32:44,945 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:32:44,945 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:32:44,947 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 21:32:44,947 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:44,947 - __main__ - INFO - {'valuation': 'fair', 'valuation_score': 0, 'trading_signal': 'neutral', 'signal_strength': 0.5, 'financial_health': 7, 'competitive_position': 'strong', 'long_term_outlook': 'positive', 'intrinsic_value_estimate': 1200000.0, 'confidence': 0.6}
2025-07-05 21:32:44,949 - __main__ - INFO - ================================================================================
2025-07-05 21:32:44,950 - __main__ - DEBUG - 记录代理输出: FAA - output_20250705_213244_c73fd70a
2025-07-05 21:32:44,951 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-05 21:32:44,951 - __main__ - INFO - 智能体 FAA 执行成功 (3.71s)
2025-07-05 21:32:44,951 - __main__ - INFO - 执行分析智能体: BOA
2025-07-05 21:32:44,952 - __main__ - DEBUG - 记录代理输入: BOA - input_20250705_213244_70c0d359
2025-07-05 21:32:44,954 - __main__ - DEBUG - 记录代理提示词: BOA - prompt_20250705_213244_2f7d5c1d
2025-07-05 21:32:44,954 - __main__ - INFO - ================================================================================
2025-07-05 21:32:44,954 - __main__ - INFO - 🤖 BOA LLM输入:
2025-07-05 21:32:44,954 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:44,954 - __main__ - INFO - 你是一个看涨倾向的市场分析师，专门构建乐观的市场展望。

你的任务是：
1. 基于前序分析师的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 提供明确的买入建议

**重要指导**：
- 积极寻找买入机会和看涨信号
- 即使在不确定情况下，也要倾向于乐观解读
- 给出明确的买入建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bullish）
- trading_signal: 交易信号（通常为buy）
- signal_strength: 信号强度（0到1）
- bullish_factors: 看涨因素列表
- target_price: 目标价位
- upside_potential: 上涨潜力（百分比）
- time_horizon: 时间框架
- risk_factors: 风险因素
- confidence: 分析信心度（0到1）

**记住**：作为看涨分析师，你的职责是找到买入理由。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📰 新闻历史信息:
  • 暂无新闻数据
💵 可用现金: $1,000,000.00

🔍 分析师输出:
  • NAA: 无新闻数据可用 (信心度: 0.5)
  • TAA: {'trend': 'neutral', 'indicators': {'RSI': 50, 'MACD': 0}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)
  • FAA: {'valuation': 'fair', 'metrics': {'PE': 20, 'PB': 2}, 'confidence': 0.5, 'source': 'mock_data'}... (信心度: 0.5)

🔮 市场展望:
  • BOA: bullish - 模拟看涨展望：基于积极新闻和技术指标。 (信心度: 0.6)
  • BeOA: bearish - 模拟看跌展望：基于负面基本面和下跌趋势。 (信心度: 0.6)
  • NOA: neutral - 模拟中性展望：市场信号混杂，无明显趋势。 (信心度: 0.6)

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:32:44,955 - __main__ - INFO - ----------------------------------------
2025-07-05 21:32:44,955 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:32:44,955 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:44,956 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:32:44,956 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:32:44,957 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:32:44,958 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:32:44,958 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:32:44,958 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:32:49,217 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=KeyboardInterrupt()
2025-07-05 21:32:49,217 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:32:49,220 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:32:49,220 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:32:49,220 - __main__ - INFO - 交易模拟器初始化完成
