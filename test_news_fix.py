#!/usr/bin/env python3
"""
测试新闻数据修复
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_news_fix():
    """测试新闻数据修复"""
    print("🧪 测试新闻数据修复")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from contribution_assessment.assessor import ContributionAssessor
        from contribution_assessment.llm_interface import LLMInterface
        
        # 创建配置
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-01",  # 只测试一天
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "verbose": True
        }
        
        print(f"📋 配置: {config}")
        
        # 创建LLM接口
        print("🔧 创建LLM接口...")
        llm_interface = LLMInterface(provider="zhipuai")
        
        # 创建ContributionAssessor
        print("🔧 创建ContributionAssessor...")
        assessor = ContributionAssessor(
            config=config,
            llm_provider="zhipuai",
            enable_opro=False
        )
        
        print("✅ ContributionAssessor创建成功")
        
        # 测试_create_mock_state方法
        print("🔍 测试状态创建...")
        state = assessor._create_mock_state()
        
        print(f"📊 状态键: {list(state.keys())}")
        print(f"📰 新闻历史: {len(state.get('news_history', {}))} 个日期")
        
        # 检查新闻数据
        news_history = state.get('news_history', {})
        if news_history:
            print(f"📊 新闻历史结构: {type(news_history)}")
            for date, news_data in news_history.items():
                print(f"  {date}: {type(news_data)} - {news_data}")
                if isinstance(news_data, dict):
                    for stock, news_list in news_data.items():
                        print(f"    {stock}: {len(news_list) if isinstance(news_list, list) else 'N/A'} 条新闻")
                        if isinstance(news_list, list) and len(news_list) > 0:
                            first_news = news_list[0]
                            if isinstance(first_news, dict):
                                title = first_news.get('title', first_news.get('headline', 'N/A'))
                                print(f"      示例: {title[:50]}...")
                elif isinstance(news_data, list) and len(news_data) > 0:
                    print(f"    直接列表: {len(news_data)} 条新闻")
                    first_news = news_data[0]
                    if isinstance(first_news, dict):
                        title = first_news.get('title', first_news.get('headline', 'N/A'))
                        print(f"      示例: {title[:50]}...")
        else:
            print("  ❌ 没有新闻数据")
        
        # 检查是否还有模拟的分析输出
        analyst_outputs = state.get('analyst_outputs', {})
        outlook_outputs = state.get('outlook_outputs', {})
        
        print(f"🔍 分析师输出: {list(analyst_outputs.keys()) if analyst_outputs else '无'}")
        print(f"🔮 展望输出: {list(outlook_outputs.keys()) if outlook_outputs else '无'}")
        
        if analyst_outputs:
            naa_output = analyst_outputs.get('NAA', {})
            print(f"📰 NAA输出: {naa_output.get('summary', 'N/A')}")
        
        # 测试NAA代理处理
        print("\n🤖 测试NAA代理...")
        from agents.analyst_agents import NewsAnalystAgent
        
        naa_agent = NewsAnalystAgent(
            llm_interface=llm_interface
        )
        
        # 格式化状态
        formatted_state = naa_agent.format_state_for_llm(state)
        print(f"📝 格式化状态长度: {len(formatted_state)} 字符")
        
        # 检查是否包含新闻信息
        if "新闻历史信息" in formatted_state and "条新闻" in formatted_state:
            print("✅ 格式化状态包含新闻信息")
            # 提取新闻部分
            news_section = formatted_state[formatted_state.find("📰 新闻历史信息"):formatted_state.find("📋 基本面数据")]
            print("     " + "\n     ".join(news_section.split("\n")[:5]))
        else:
            print("❌ 格式化状态不包含新闻信息")
        
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始新闻数据修复测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_news_fix()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 测试成功！")
    else:
        print("💥 测试失败！")
    print("=" * 50)
