"""
每周OPRO管理器 (Weekly OPRO Manager)

本模块实现每周OPRO提示词更新机制，包括：
1. 每周提示词优化和保存
2. A/B测试框架（原提示词 vs 优化提示词）
3. 基于夏普率的性能评估
4. 提示词版本管理和历史记录

主要功能：
- 周期性提示词优化
- A/B测试实验设计
- 性能对比分析
- 提示词版本控制
"""

import json
import logging
import os
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path

class WeeklyOPROManager:
    """
    每周OPRO管理器
    
    负责管理每周的提示词优化循环，包括：
    - 提示词优化和保存
    - A/B测试实验管理
    - 性能评估和对比
    - 历史记录维护
    """
    
    def __init__(self, 
                 base_data_dir: str = "data",
                 opro_optimizer=None,
                 assessor=None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化每周OPRO管理器
        
        参数:
            base_data_dir: 数据存储基础目录
            opro_optimizer: OPRO优化器实例
            assessor: 贡献度评估器实例
            logger: 日志记录器
        """
        self.base_data_dir = Path(base_data_dir)
        self.opro_optimizer = opro_optimizer
        self.assessor = assessor
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建必要的目录结构
        self.weekly_experiments_dir = self.base_data_dir / "weekly_experiments"
        self.prompt_versions_dir = self.base_data_dir / "prompt_versions"
        self.ab_test_results_dir = self.base_data_dir / "ab_test_results"
        
        for dir_path in [self.weekly_experiments_dir, self.prompt_versions_dir, self.ab_test_results_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"每周OPRO管理器初始化完成，数据目录: {self.base_data_dir}")
    
    def run_weekly_optimization_cycle(self, 
                                    target_agents: List[str],
                                    current_week_start: str,
                                    force_optimization: bool = False) -> Dict[str, Any]:
        """
        运行每周优化循环
        
        参数:
            target_agents: 目标代理列表
            current_week_start: 当前周开始日期 (YYYY-MM-DD)
            force_optimization: 是否强制优化
            
        返回:
            优化结果字典
        """
        self.logger.info(f"开始每周优化循环 - 周期: {current_week_start}")
        
        try:
            # 1. 检查是否需要优化
            if not force_optimization and not self._should_run_optimization(current_week_start):
                self.logger.info("本周已完成优化，跳过")
                return {"status": "skipped", "reason": "already_optimized"}
            
            # 2. 获取当前提示词
            current_prompts = self._get_current_prompts(target_agents)
            
            # 3. 运行OPRO优化
            optimization_results = self._run_opro_optimization(target_agents, current_prompts)
            
            # 4. 保存提示词版本
            version_info = self._save_prompt_versions(
                current_week_start, 
                target_agents, 
                current_prompts, 
                optimization_results
            )
            
            # 5. 准备下周A/B测试
            ab_test_config = self._prepare_ab_test_config(
                current_week_start, 
                target_agents, 
                version_info
            )
            
            # 6. 保存优化记录
            optimization_record = {
                "week_start": current_week_start,
                "target_agents": target_agents,
                "optimization_timestamp": datetime.now().isoformat(),
                "version_info": version_info,
                "ab_test_config": ab_test_config,
                "optimization_results": optimization_results
            }
            
            self._save_optimization_record(current_week_start, optimization_record)
            
            self.logger.info(f"每周优化循环完成 - 周期: {current_week_start}")
            
            return {
                "status": "success",
                "week_start": current_week_start,
                "optimized_agents": target_agents,
                "version_info": version_info,
                "ab_test_config": ab_test_config
            }
            
        except Exception as e:
            self.logger.error(f"每周优化循环失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_weekly_ab_test(self, 
                          target_agents: List[str],
                          test_week_start: str,
                          test_duration_days: int = 7) -> Dict[str, Any]:
        """
        运行每周A/B测试
        
        参数:
            target_agents: 目标代理列表
            test_week_start: 测试周开始日期
            test_duration_days: 测试持续天数
            
        返回:
            A/B测试结果
        """
        self.logger.info(f"开始A/B测试 - 测试周: {test_week_start}")
        
        try:
            # 1. 加载A/B测试配置
            ab_config = self._load_ab_test_config(test_week_start, target_agents)
            if not ab_config:
                return {"status": "error", "error": "No A/B test configuration found"}
            
            # 2. 运行原版本测试
            original_results = self._run_version_test(
                ab_config["original_versions"], 
                test_week_start, 
                test_duration_days,
                "original"
            )
            
            # 3. 运行优化版本测试
            optimized_results = self._run_version_test(
                ab_config["optimized_versions"], 
                test_week_start, 
                test_duration_days,
                "optimized"
            )
            
            # 4. 计算性能对比
            performance_comparison = self._calculate_performance_comparison(
                original_results, 
                optimized_results
            )
            
            # 5. 保存A/B测试结果
            ab_test_result = {
                "test_week_start": test_week_start,
                "test_duration_days": test_duration_days,
                "target_agents": target_agents,
                "original_results": original_results,
                "optimized_results": optimized_results,
                "performance_comparison": performance_comparison,
                "test_timestamp": datetime.now().isoformat()
            }
            
            self._save_ab_test_result(test_week_start, ab_test_result)
            
            # 6. 决定是否采用优化版本
            adoption_decision = self._make_adoption_decision(performance_comparison)
            
            self.logger.info(f"A/B测试完成 - 测试周: {test_week_start}")
            
            return {
                "status": "success",
                "test_week_start": test_week_start,
                "performance_comparison": performance_comparison,
                "adoption_decision": adoption_decision,
                "ab_test_result": ab_test_result
            }
            
        except Exception as e:
            self.logger.error(f"A/B测试失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def _should_run_optimization(self, week_start: str) -> bool:
        """检查是否应该运行优化"""
        optimization_file = self.weekly_experiments_dir / f"optimization_{week_start}.json"
        return not optimization_file.exists()
    
    def _get_current_prompts(self, target_agents: List[str]) -> Dict[str, str]:
        """获取当前提示词"""
        current_prompts = {}
        
        for agent_id in target_agents:
            if self.assessor and hasattr(self.assessor, '_get_default_prompt_for_agent'):
                prompt = self.assessor._get_default_prompt_for_agent(agent_id)
            else:
                # 默认提示词
                default_prompts = {
                    "NAA": "你是一个专业的新闻分析师，分析市场新闻对股票的影响。",
                    "TAA": "你是一个专业的技术分析师，通过图表和技术指标分析股票趋势。",
                    "FAA": "你是一个专业的基本面分析师，分析公司财务状况和内在价值。",
                    "BOA": "你是一个看涨分析师，构建乐观的市场展望。",
                    "BeOA": "你是一个看跌分析师，识别市场风险和负面因素。",
                    "NOA": "你是一个中性观察者，提供平衡的市场分析。",
                    "TRA": "你是一个专业的积极交易员，负责做出最终的交易决策。你的目标是通过主动交易获得收益，避免过度保守的持有策略。当分析师给出明确信号时，要积极执行买卖操作。"
                }
                prompt = default_prompts.get(agent_id, f"默认智能体提示词 - {agent_id}")
            
            current_prompts[agent_id] = prompt
        
        return current_prompts
    
    def _run_opro_optimization(self, target_agents: List[str], current_prompts: Dict[str, str]) -> Dict[str, Any]:
        """运行OPRO优化"""
        if not self.opro_optimizer:
            raise ValueError("OPRO优化器未初始化")
        
        optimization_results = {}
        
        for agent_id in target_agents:
            current_prompt = current_prompts.get(agent_id, "")
            
            try:
                result = self.opro_optimizer.optimize_agent_prompt(agent_id, current_prompt)
                optimization_results[agent_id] = result
                self.logger.info(f"代理 {agent_id} 优化完成")
            except Exception as e:
                self.logger.error(f"代理 {agent_id} 优化失败: {e}")
                optimization_results[agent_id] = {"status": "error", "error": str(e)}
        
        return optimization_results

    def _save_prompt_versions(self,
                             week_start: str,
                             target_agents: List[str],
                             original_prompts: Dict[str, str],
                             optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """保存提示词版本"""
        version_info = {}

        for agent_id in target_agents:
            original_prompt = original_prompts.get(agent_id, "")
            optimization_result = optimization_results.get(agent_id, {})

            # 生成版本哈希
            original_hash = hashlib.md5(original_prompt.encode('utf-8')).hexdigest()

            optimized_prompt = ""
            optimized_hash = ""

            if optimization_result.get("status") != "error":
                optimized_prompt = optimization_result.get("optimized_prompt", "")
                optimized_hash = hashlib.md5(optimized_prompt.encode('utf-8')).hexdigest()

            # 保存版本文件
            version_data = {
                "week_start": week_start,
                "agent_id": agent_id,
                "original_version": {
                    "prompt": original_prompt,
                    "hash": original_hash,
                    "timestamp": datetime.now().isoformat()
                },
                "optimized_version": {
                    "prompt": optimized_prompt,
                    "hash": optimized_hash,
                    "timestamp": datetime.now().isoformat(),
                    "optimization_result": optimization_result
                }
            }

            version_file = self.prompt_versions_dir / f"{agent_id}_versions_{week_start}.json"
            with open(version_file, 'w', encoding='utf-8') as f:
                json.dump(version_data, f, ensure_ascii=False, indent=2)

            version_info[agent_id] = {
                "original_hash": original_hash,
                "optimized_hash": optimized_hash,
                "version_file": str(version_file)
            }

        return version_info

    def _prepare_ab_test_config(self,
                               week_start: str,
                               target_agents: List[str],
                               version_info: Dict[str, Any]) -> Dict[str, Any]:
        """准备A/B测试配置"""
        # 计算下周开始日期
        current_date = datetime.strptime(week_start, "%Y-%m-%d")
        next_week_start = (current_date + timedelta(days=7)).strftime("%Y-%m-%d")

        ab_config = {
            "test_week_start": next_week_start,
            "optimization_week_start": week_start,
            "target_agents": target_agents,
            "original_versions": {},
            "optimized_versions": {},
            "test_config": {
                "duration_days": 7,
                "evaluation_metric": "sharpe_ratio",
                "confidence_level": 0.95
            }
        }

        # 加载版本信息
        for agent_id in target_agents:
            version_file = Path(version_info[agent_id]["version_file"])
            if version_file.exists():
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)

                ab_config["original_versions"][agent_id] = version_data["original_version"]
                ab_config["optimized_versions"][agent_id] = version_data["optimized_version"]

        # 保存A/B测试配置
        ab_config_file = self.ab_test_results_dir / f"ab_config_{next_week_start}.json"
        with open(ab_config_file, 'w', encoding='utf-8') as f:
            json.dump(ab_config, f, ensure_ascii=False, indent=2)

        return ab_config

    def _save_optimization_record(self, week_start: str, record: Dict[str, Any]):
        """保存优化记录"""
        record_file = self.weekly_experiments_dir / f"optimization_{week_start}.json"
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

    def _load_ab_test_config(self, test_week_start: str, target_agents: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
        """加载A/B测试配置"""
        ab_config_file = self.ab_test_results_dir / f"ab_config_{test_week_start}.json"

        if not ab_config_file.exists():
            self.logger.warning(f"A/B测试配置文件不存在: {ab_config_file}")
            return None

        try:
            with open(ab_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载A/B测试配置失败: {e}")
            return None

    def _run_version_test(self,
                         version_prompts: Dict[str, Any],
                         test_week_start: str,
                         test_duration_days: int,
                         version_type: str) -> Dict[str, Any]:
        """运行版本测试"""
        if not self.assessor:
            raise ValueError("评估器未初始化")

        self.logger.info(f"运行{version_type}版本测试 - 测试周: {test_week_start}")

        try:
            # 准备测试配置
            test_config = {
                "start_date": test_week_start,
                "end_date": self._calculate_end_date(test_week_start, test_duration_days),
                "version_type": version_type,
                "prompts": {agent_id: version_data["prompt"]
                           for agent_id, version_data in version_prompts.items()}
            }

            # 运行评估
            evaluation_result = self.assessor.run(
                target_agents=list(version_prompts.keys()),
                max_coalitions=15
            )

            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics(evaluation_result)

            return {
                "version_type": version_type,
                "test_config": test_config,
                "evaluation_result": evaluation_result,
                "performance_metrics": performance_metrics,
                "test_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"{version_type}版本测试失败: {e}")
            return {"status": "error", "error": str(e)}

    def _calculate_end_date(self, start_date: str, duration_days: int) -> str:
        """计算结束日期"""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = start + timedelta(days=duration_days - 1)
        return end.strftime("%Y-%m-%d")

    def _calculate_performance_metrics(self, evaluation_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能指标"""
        metrics = {}

        # 提取Shapley值
        shapley_values = evaluation_result.get("shapley_values", {})
        if shapley_values:
            metrics["shapley_values"] = shapley_values
            metrics["average_shapley"] = np.mean(list(shapley_values.values()))
            metrics["total_shapley"] = sum(shapley_values.values())

        # 提取交易性能
        trading_performance = evaluation_result.get("trading_performance", {})
        if trading_performance:
            metrics["sharpe_ratio"] = trading_performance.get("sharpe_ratio", 0.0)
            metrics["total_return"] = trading_performance.get("total_return", 0.0)
            metrics["volatility"] = trading_performance.get("volatility", 0.0)
            metrics["max_drawdown"] = trading_performance.get("max_drawdown", 0.0)

        return metrics

    def _calculate_performance_comparison(self,
                                        original_results: Dict[str, Any],
                                        optimized_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能对比"""
        comparison = {
            "comparison_timestamp": datetime.now().isoformat(),
            "metrics_comparison": {},
            "improvement_analysis": {},
            "statistical_significance": {}
        }

        # 获取性能指标
        original_metrics = original_results.get("performance_metrics", {})
        optimized_metrics = optimized_results.get("performance_metrics", {})

        # 对比关键指标
        key_metrics = ["sharpe_ratio", "total_return", "average_shapley", "total_shapley"]

        for metric in key_metrics:
            original_value = original_metrics.get(metric, 0.0)
            optimized_value = optimized_metrics.get(metric, 0.0)

            if original_value != 0:
                improvement_pct = ((optimized_value - original_value) / abs(original_value)) * 100
            else:
                improvement_pct = 0.0 if optimized_value == 0 else float('inf')

            comparison["metrics_comparison"][metric] = {
                "original": original_value,
                "optimized": optimized_value,
                "improvement": optimized_value - original_value,
                "improvement_percentage": improvement_pct
            }

        # 改进分析
        sharpe_improvement = comparison["metrics_comparison"].get("sharpe_ratio", {}).get("improvement", 0.0)
        return_improvement = comparison["metrics_comparison"].get("total_return", {}).get("improvement", 0.0)

        comparison["improvement_analysis"] = {
            "overall_improvement": sharpe_improvement > 0 and return_improvement > 0,
            "sharpe_improved": sharpe_improvement > 0,
            "return_improved": return_improvement > 0,
            "primary_metric_improvement": sharpe_improvement,
            "recommendation": "adopt" if sharpe_improvement > 0.05 else "reject" if sharpe_improvement < -0.05 else "neutral"
        }

        return comparison

    def _make_adoption_decision(self, performance_comparison: Dict[str, Any]) -> Dict[str, Any]:
        """做出采用决策"""
        improvement_analysis = performance_comparison.get("improvement_analysis", {})

        # 决策逻辑
        sharpe_improvement = improvement_analysis.get("primary_metric_improvement", 0.0)
        recommendation = improvement_analysis.get("recommendation", "neutral")

        decision = {
            "adopt_optimized_version": recommendation == "adopt",
            "decision_reason": "",
            "confidence_level": 0.0,
            "decision_timestamp": datetime.now().isoformat()
        }

        if sharpe_improvement > 0.05:
            decision["decision_reason"] = f"夏普率显著改善 (+{sharpe_improvement:.4f})"
            decision["confidence_level"] = min(0.95, 0.5 + abs(sharpe_improvement) * 2)
        elif sharpe_improvement < -0.05:
            decision["decision_reason"] = f"夏普率显著下降 ({sharpe_improvement:.4f})"
            decision["confidence_level"] = min(0.95, 0.5 + abs(sharpe_improvement) * 2)
        else:
            decision["decision_reason"] = f"性能变化不显著 ({sharpe_improvement:.4f})"
            decision["confidence_level"] = 0.3

        return decision

    def _save_ab_test_result(self, test_week_start: str, result: Dict[str, Any]):
        """保存A/B测试结果"""
        result_file = self.ab_test_results_dir / f"ab_result_{test_week_start}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

    def get_weekly_optimization_history(self, weeks_back: int = 4) -> List[Dict[str, Any]]:
        """获取每周优化历史"""
        history = []

        # 获取最近几周的优化记录
        current_date = datetime.now()
        for i in range(weeks_back):
            week_start = (current_date - timedelta(weeks=i)).strftime("%Y-%m-%d")
            week_start = self._get_week_start_date(week_start)

            optimization_file = self.weekly_experiments_dir / f"optimization_{week_start}.json"
            ab_result_file = self.ab_test_results_dir / f"ab_result_{week_start}.json"

            week_data = {"week_start": week_start}

            # 加载优化记录
            if optimization_file.exists():
                try:
                    with open(optimization_file, 'r', encoding='utf-8') as f:
                        week_data["optimization"] = json.load(f)
                except Exception as e:
                    self.logger.error(f"加载优化记录失败 {week_start}: {e}")

            # 加载A/B测试结果
            if ab_result_file.exists():
                try:
                    with open(ab_result_file, 'r', encoding='utf-8') as f:
                        week_data["ab_test"] = json.load(f)
                except Exception as e:
                    self.logger.error(f"加载A/B测试结果失败 {week_start}: {e}")

            if len(week_data) > 1:  # 有数据才添加
                history.append(week_data)

        return history

    def _get_week_start_date(self, date_str: str) -> str:
        """获取周开始日期（周一）"""
        date = datetime.strptime(date_str, "%Y-%m-%d")
        # 获取周一的日期
        days_since_monday = date.weekday()
        monday = date - timedelta(days=days_since_monday)
        return monday.strftime("%Y-%m-%d")

    def generate_weekly_report(self, week_start: str) -> Dict[str, Any]:
        """生成每周报告"""
        report = {
            "week_start": week_start,
            "report_timestamp": datetime.now().isoformat(),
            "optimization_summary": {},
            "ab_test_summary": {},
            "recommendations": []
        }

        # 加载优化记录
        optimization_file = self.weekly_experiments_dir / f"optimization_{week_start}.json"
        if optimization_file.exists():
            try:
                with open(optimization_file, 'r', encoding='utf-8') as f:
                    optimization_data = json.load(f)
                    report["optimization_summary"] = self._summarize_optimization(optimization_data)
            except Exception as e:
                self.logger.error(f"加载优化记录失败: {e}")

        # 加载A/B测试结果
        ab_result_file = self.ab_test_results_dir / f"ab_result_{week_start}.json"
        if ab_result_file.exists():
            try:
                with open(ab_result_file, 'r', encoding='utf-8') as f:
                    ab_data = json.load(f)
                    report["ab_test_summary"] = self._summarize_ab_test(ab_data)
            except Exception as e:
                self.logger.error(f"加载A/B测试结果失败: {e}")

        # 生成建议
        report["recommendations"] = self._generate_recommendations(report)

        return report

    def _summarize_optimization(self, optimization_data: Dict[str, Any]) -> Dict[str, Any]:
        """总结优化结果"""
        return {
            "optimized_agents": optimization_data.get("target_agents", []),
            "optimization_timestamp": optimization_data.get("optimization_timestamp", ""),
            "success_count": len([r for r in optimization_data.get("optimization_results", {}).values()
                                if r.get("status") != "error"]),
            "total_agents": len(optimization_data.get("target_agents", []))
        }

    def _summarize_ab_test(self, ab_data: Dict[str, Any]) -> Dict[str, Any]:
        """总结A/B测试结果"""
        performance_comparison = ab_data.get("performance_comparison", {})
        improvement_analysis = performance_comparison.get("improvement_analysis", {})

        return {
            "test_duration": ab_data.get("test_duration_days", 0),
            "overall_improvement": improvement_analysis.get("overall_improvement", False),
            "sharpe_improvement": improvement_analysis.get("primary_metric_improvement", 0.0),
            "recommendation": improvement_analysis.get("recommendation", "neutral"),
            "adoption_decision": ab_data.get("adoption_decision", {})
        }

    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []

        ab_summary = report.get("ab_test_summary", {})
        if ab_summary.get("overall_improvement", False):
            recommendations.append("建议采用优化后的提示词版本，性能有显著改善")
        elif ab_summary.get("sharpe_improvement", 0) < -0.05:
            recommendations.append("建议保持原版本提示词，优化版本性能下降")
        else:
            recommendations.append("性能变化不显著，可考虑进一步优化或保持现状")

        optimization_summary = report.get("optimization_summary", {})
        success_rate = optimization_summary.get("success_count", 0) / max(optimization_summary.get("total_agents", 1), 1)
        if success_rate < 0.8:
            recommendations.append("部分代理优化失败，建议检查优化参数和历史数据")

        return recommendations
