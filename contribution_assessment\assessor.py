"""
主协调器 (Main Orchestrator)

本模块实现了贡献度评估系统的主协调器，统筹整个四阶段流程：
1. 分析缓存阶段：运行并缓存分析智能体结果
2. 联盟生成阶段：生成和剪枝智能体联盟
3. 交易模拟阶段：为每个有效联盟运行交易模拟
4. Shapley值计算阶段：计算各智能体的最终贡献度

主要功能：
- 协调四个核心模块的执行顺序
- 管理模块间的数据流
- 提供完整的贡献度评估工作流
- 支持灵活的配置和自定义参数
"""

import time
from typing import Dict, Any, List, Set, Optional
from datetime import datetime
import logging
import json
import concurrent.futures
import threading
from collections import defaultdict

# 导入核心模块
from .analysis_cache import AnalysisCache
from .coalition_manager import CoalitionManager
from .trading_simulator import TradingSimulator
from .shapley_calculator import ShapleyCalculator
from .llm_interface import LLMInterface

# 导入OPRO相关模块
try:
    from .opro_optimizer import OPROOptimizer
    from .historical_score_manager import HistoricalScoreManager
    OPRO_AVAILABLE = True
except ImportError:
    OPRO_AVAILABLE = False


class ContributionAssessor:
    """
    贡献度评估主协调器
    
    统筹整个多智能体贡献度评估流程，协调四个核心模块的执行，
    管理数据流，并提供完整的Shapley值计算结果。
    """
    
    def __init__(self,
                 config: Optional[Dict[str, Any]] = None,
                 agents: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None,
                 llm_provider: Optional[str] = None,
                 enable_opro: bool = False,
                 opro_config: Optional[Dict[str, Any]] = None,
                 interaction_logger=None):
        """
        初始化贡献度评估器
        
        参数:
            config: 系统配置字典，包含交易环境、智能体等配置
            agents: 智能体实例字典，格式为 {agent_id: agent_instance}
            logger: 日志记录器，如果为None则创建默认记录器
            llm_provider: LLM提供商名称
            enable_opro: 是否启用OPRO提示词优化功能
            opro_config: OPRO优化器配置
        """
        self.logger = logger or self._create_default_logger()

        # 正确合并配置：先获取默认配置，然后用传入的配置更新
        self.config = self._get_default_config()
        if config:
            self.config.update(config)

        self.agents = agents or {}
        self.interaction_logger = interaction_logger

        # 执行统计信息
        self._stats = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "last_run_time": 0.0,
            "average_run_time": 0.0,
            "last_run": None
        }
        
        # 默认智能体配置
        self.default_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        self.analyst_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]  # 需要LLM分析的智能体
        self.core_analyst_agents = ["NAA", "TAA", "FAA"]  # 用于联盟剪枝的核心分析智能体
        self.trader_agent = "TRA"
        
        # 并发配置
        self.max_concurrent_api_calls = 30  # LLM API并发限制
        self.enable_concurrent_execution = self.config.get("enable_concurrent_execution", True)
        self._concurrent_stats = {
            "total_concurrent_tasks": 0,
            "successful_concurrent_tasks": 0,
            "failed_concurrent_tasks": 0,
            "concurrent_execution_time": 0.0
        }
        
        # 初始化核心模块
        self._initialize_modules()
        
        # 初始化LLM接口
        self.llm_interface = None
        if llm_provider:
            self.llm_interface = LLMInterface(provider=llm_provider, logger=self.logger)
        
        # 初始化OPRO相关组件
        self.enable_opro = enable_opro and OPRO_AVAILABLE
        self.opro_optimizer = None
        self.historical_score_manager = None
        self.opro_config = opro_config or self._get_default_opro_config()
        
        if self.enable_opro:
            self._initialize_opro_components()
        elif enable_opro and not OPRO_AVAILABLE:
            self.logger.warning("OPRO功能请求但模块不可用，已禁用OPRO功能")
        
        self.logger.info(f"贡献度评估器初始化完成 (OPRO: {'启用' if self.enable_opro else '禁用'})")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ContributionAssessor")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "start_date": "2023-01-01",
            "end_date": "2023-04-01",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "risk_free_rate": 0.02,
            "simulation_days": None,  # None表示使用配置的日期范围
            "fail_on_large_gaps": False,
            "fill_date_gaps": True,
            "max_coalitions_to_simulate": None,  # None表示模拟所有有效联盟
            "enable_parallel_simulation": False,  # 是否启用并行模拟
            "verbose": True
        }
    
    def _get_default_opro_config(self) -> Dict[str, Any]:
        """获取默认OPRO配置"""
        return {
            "optimization_frequency": "weekly",  # 优化频率
            "candidates_per_generation": 8,
            "historical_weeks_to_consider": 10,
            "temperature": 1.0,
            "max_optimization_iterations": 50,
            "convergence_threshold": 0.001,
            "prompt_length_limit": 500,
            "evaluation_timeout": 300,
            "enable_cache": True,
            "cache_ttl": 3600,
            "parallel_evaluation": True,
            "max_workers": 4,
            "auto_optimize_after_evaluation": True,  # 评估后自动优化
            "min_improvement_threshold": 0.01,  # 最小改进阈值
            "enable_ab_testing": False,  # 是否启用A/B测试
            "rollback_on_degradation": True  # 性能下降时自动回滚
        }
    
    def _initialize_opro_components(self):
        """初始化OPRO相关组件"""
        try:
            # 初始化历史得分管理器
            self.historical_score_manager = HistoricalScoreManager(
                results_base_path=self.opro_config.get("results_base_path", "results/periodic_shapley"),
                db_path=self.opro_config.get("opro_db_path", "results/opro_optimization.db"),
                logger=self.logger
            )
            
            # 初始化OPRO优化器
            if self.llm_interface:
                self.opro_optimizer = OPROOptimizer(
                    llm_interface=self.llm_interface,
                    historical_score_manager=self.historical_score_manager,
                    logger=self.logger,
                    config=self.opro_config,
                    prompt_tracker=self.prompt_tracker
                )
                
                self.logger.info("OPRO组件初始化成功")
            else:
                self.logger.warning("LLM接口未初始化，无法启用OPRO优化器")
                self.enable_opro = False
                
        except Exception as e:
            self.logger.error(f"OPRO组件初始化失败: {e}")
            self.enable_opro = False
    
    def _initialize_modules(self) -> None:
        """初始化核心模块"""
        # 1. 分析缓存模块
        self.analysis_cache = AnalysisCache(logger=self.logger)

        # 2. 联盟管理模块
        self.coalition_manager = CoalitionManager(logger=self.logger)

        # 3. 联盟存储管理模块
        from .coalition_storage_manager import CoalitionStorageManager
        self.storage_manager = CoalitionStorageManager(logger=self.logger)

        # 4. 交易模拟模块
        self.trading_simulator = TradingSimulator(
            base_config=self.config,
            logger=self.logger,
            storage_manager=self.storage_manager
        )

        # 5. Shapley值计算模块
        self.shapley_calculator = ShapleyCalculator(logger=self.logger)

        # 5. 提示词跟踪模块
        try:
            from data.prompt_optimization_tracker import PromptOptimizationTracker
            from data.comprehensive_storage_manager import ComprehensiveStorageManager

            # 初始化存储管理器
            storage_manager = ComprehensiveStorageManager(logger=self.logger)

            # 初始化提示词跟踪器
            self.prompt_tracker = PromptOptimizationTracker(
                storage_manager=storage_manager,
                logger=self.logger
            )

        except ImportError as e:
            self.logger.warning(f"提示词跟踪模块导入失败: {e}")
            self.prompt_tracker = None
        
        self.logger.debug("所有核心模块初始化完成")
    
    def run(self, 
           agents: Optional[Dict[str, Any]] = None,
           target_agents: Optional[List[str]] = None,
           max_coalitions: Optional[int] = None) -> Dict[str, Any]:
        """
        运行完整的贡献度评估流程
        
        执行四个阶段：
        1. 分析缓存阶段
        2. 联盟生成阶段  
        3. 交易模拟阶段
        4. Shapley值计算阶段
        
        参数:
            agents: 智能体实例字典，如果为None则使用初始化时提供的智能体
            target_agents: 目标智能体列表，如果为None则使用默认智能体
            max_coalitions: 最大模拟联盟数量，用于限制计算量
            
        返回:
            包含Shapley值和执行统计的结果字典
        """
        start_time = time.time()
        
        # 创建或使用提供的智能体
        if agents:
            active_agents = agents
        elif self.agents:
            active_agents = self.agents
        else:
            # 创建默认的LLM智能体
            active_agents = self._create_default_llm_agents()
        
        target_agent_list = target_agents or self.default_agents
        
        self.logger.info(f"开始贡献度评估流程")
        self.logger.info(f"目标智能体: {target_agent_list}")
        self.logger.info(f"可用智能体实例: {list(active_agents.keys()) if active_agents else '无（将使用模拟智能体）'}")

        # 开始新的Shapley实验
        experiment_config = {
            "target_agents": target_agent_list,
            "max_coalitions": max_coalitions,
            "config": self.config,
            "start_time": start_time
        }
        experiment_id = self.storage_manager.start_new_experiment(experiment_config)
        self.logger.info(f"开始新实验: {experiment_id}")

        try:
            # 阶段1: 分析缓存
            self.logger.info("=" * 50)
            self.logger.info("阶段1: 分析缓存")

            # 优先使用智能体实例（如果可用），否则使用LLM直接分析，最后回退到模拟数据
            if active_agents and any(agent_id in active_agents for agent_id in self.analyst_agents):
                self.logger.info("使用智能体实例进行分析...")
                cache_result = self._run_analysis_caching_phase(active_agents)
            elif self.llm_interface and self.llm_interface.client:
                self.logger.info("使用LLM直接分析...")
                cache_result = self._run_llm_analysis_phase()
            else:
                self.logger.warning("LLM接口不可用且无智能体实例，使用模拟数据...")
                cache_result = self._run_analysis_caching_phase({})
            
            # 阶段2: 周期性联盟生成与交易模拟
            self.logger.info("=" * 50)
            self.logger.info("阶段2: 周期性联盟生成与交易模拟")
            weekly_simulation_result = self._run_weekly_coalition_simulation_phase(
                target_agent_list, active_agents, max_coalitions
            )

            # 阶段3: 最终Shapley值计算
            self.logger.info("=" * 50)
            self.logger.info("阶段3: 最终Shapley值计算")
            shapley_result = self._run_shapley_calculation_phase(
                target_agent_list,
                weekly_simulation_result["final_coalition_values"]
            )
            
            # 汇总结果
            total_time = time.time() - start_time
            result = self._compile_weekly_final_result(
                cache_result, weekly_simulation_result, shapley_result, total_time
            )
            
            # 更新统计信息
            self._update_stats(total_time, success=True)
            
            self.logger.info("=" * 50)
            self.logger.info(f"贡献度评估完成，总耗时: {total_time:.2f}s")
            
            return result
            
        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error(f"贡献度评估失败: {e}")
            self._update_stats(total_time, success=False)
            
            # 返回错误结果
            return {
                "success": False,
                "error": str(e),
                "execution_time": total_time,
                "shapley_values": {},
                "phase_results": {}
            }

    def _run_llm_analysis_phase(self) -> Dict[str, Any]:
        """
        使用LLM运行分析缓存阶段

        返回:
            缓存阶段的执行结果
        """
        self.logger.info("开始LLM分析缓存阶段...")
        start_time = time.time()

        # 检查LLM接口是否可用
        if not self.llm_interface or not self.llm_interface.client:
            self.logger.error("LLM接口不可用，无法执行LLM分析阶段")
            return {
                "success": False,
                "error": "LLM接口不可用",
                "execution_time": time.time() - start_time,
                "method": "llm_analysis"
            }

        mock_state = self._create_mock_state()

        for agent_id in self.analyst_agents:
            prompt = self._create_llm_prompt(agent_id, mock_state)

            analysis_result = self.llm_interface.analyze(
                prompt,
                model="glm-4-flash"
            )
            
            if analysis_result:
                self.analysis_cache.store(agent_id, analysis_result)
                self.logger.info(f"LLM为 {agent_id} 生成的分析已缓存")
            else:
                self.logger.error(f"LLM为 {agent_id} 生成分析失败，使用模拟数据替代")
                mock_data = self._get_mock_analysis_data(agent_id)
                self.analysis_cache.store(agent_id, mock_data)

        execution_time = time.time() - start_time
        return {
            "success": True,
            "cached_agents": list(self.analysis_cache.get_cached_agents()),
            "execution_time": execution_time,
            "method": "llm_analysis"
        }

    def _create_llm_prompt(self, agent_id: str, state: Dict[str, Any]) -> str:
        """
        为LLM创建特定角色的分析提示
        
        参数:
            agent_id: 智能体ID (NAA, TAA, FAA)
            state: 当前市场状态
            
        返回:
            生成的提示字符串
        """
        
        base_prompt = f"""
        角色: 你是一个{agent_id} (Agent), 在一个多智能体金融分析系统中工作。
        任务: 根据以下市场数据，生成一份JSON格式的分析报告。
        
        市场数据:
        {json.dumps(state, indent=2, ensure_ascii=False)}
        """
        
        if agent_id == "NAA":
            return base_prompt + """
            作为新闻分析智能体 (NAA), 你的报告应包含:
            - "sentiment": 市场情绪 (正向/负向/中性).
            - "summary": 关键新闻摘要.
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        elif agent_id == "TAA":
            return base_prompt + """
            作为技术分析智能体 (TAA), 你的报告应包含:
            - "trend": 价格趋势 (上涨/下跌/盘整).
            - "indicators": 关键技术指标 (例如 {"RSI": 50, "MACD": 0}).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        elif agent_id == "FAA":
            return base_prompt + """
            作为基本面分析智能体 (FAA), 你的报告应包含:
            - "valuation": 估值水平 (低估/高估/合理).
            - "metrics": 关键财务指标 (例如 {"PE": 20, "PB": 2}).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".

            请严格按照此JSON格式输出。
            """
        elif agent_id == "BOA":
            return base_prompt + """
            作为看涨展望智能体 (BOA), 你的报告应基于新闻、技术和基本面分析，提供看涨的市场展望。
            报告应包含:
            - "outlook": 市场展望 (看涨).
            - "reasoning": 看涨理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".

            请严格按照此JSON格式输出。
            """
        elif agent_id == "BeOA":
            return base_prompt + """
            作为看跌展望智能体 (BeOA), 你的报告应基于新闻、技术和基本面分析，提供看跌的市场展望。
            报告应包含:
            - "outlook": 市场展望 (看跌).
            - "reasoning": 看跌理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".

            请严格按照此JSON格式输出。
            """
        elif agent_id == "NOA":
            return base_prompt + """
            作为中性展望智能体 (NOA), 你的报告应基于新闻、技术和基本面分析，提供中性的市场展望。
            报告应包含:
            - "outlook": 市场展望 (中性).
            - "reasoning": 中性理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".

            请严格按照此JSON格式输出。
            """
        elif agent_id == "TRA":
            return base_prompt + """
            作为交易智能体 (TRA), 你的报告应基于所有展望层智能体 (BOA, BeOA, NOA) 的分析，提供最终的交易决策。
            报告应包含:
            - "action": 交易动作 (BUY/SELL/HOLD).
            - "quantity": 交易数量 (如果为BUY/SELL).
            - "reasoning": 决策理由 (基于BOA, BeOA, NOA的展望).
            - "confidence": 决策置信度 (0.0 to 1.0).
            - "source": "llm_analysis".

            请严格按照此JSON格式输出。
            """
        else:
            return base_prompt

    def _run_analysis_caching_phase(self, agents: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行分析缓存阶段
        
        参数:
            agents: 智能体实例字典
            
        返回:
            缓存阶段的执行结果
        """
        self.logger.info("开始分析缓存阶段...")
        
        # 筛选分析智能体
        analyst_agent_instances = {
            agent_id: agent_instance 
            for agent_id, agent_instance in agents.items() 
            if agent_id in self.analyst_agents
        }
        
        if not analyst_agent_instances:
            self.logger.warning("未提供分析智能体实例，将使用模拟数据")
            # 填充模拟的分析结果
            for agent_id in self.analyst_agents:
                mock_data = self._get_mock_analysis_data(agent_id)
                self.analysis_cache.store(agent_id, mock_data)
            
            return {
                "success": True,
                "cached_agents": self.analyst_agents,
                "execution_time": 0.0,
                "method": "mock_data"
            }
        
        # 创建模拟状态（用于智能体执行）
        mock_state = self._create_mock_state()
        
        # 运行分析智能体并缓存结果
        cache_result = self.analysis_cache.populate_from_agents(
            analyst_agent_instances, mock_state
        )
        
        self.logger.info(f"分析缓存阶段完成: 成功缓存 {len(cache_result['successful_agents'])} 个智能体")
        
        return {
            "success": True,
            "cache_result": cache_result,
            "cached_agents": [agent["agent_id"] for agent in cache_result["successful_agents"]],
            "execution_time": cache_result["execution_time"]
        }
    
    def _run_coalition_generation_phase(self, target_agents: List[str]) -> Dict[str, Any]:
        """
        运行联盟生成阶段
        
        参数:
            target_agents: 目标智能体列表
            
        返回:
            联盟生成阶段的执行结果
        """
        self.logger.info("开始联盟生成阶段...")
        
        # 确定实际可用的分析智能体（从目标智能体中过滤出分析智能体）
        available_analyst_agents = [agent for agent in target_agents 
                                   if agent in self.core_analyst_agents]
        
        # 生成和剪枝联盟
        valid_coalitions, pruned_coalitions = self.coalition_manager.generate_pruned_coalitions(
            target_agents, available_analyst_agents, self.trader_agent
        )
        
        # 分析联盟结构
        coalition_analysis = self.coalition_manager.analyze_coalition_structure(
            valid_coalitions, target_agents
        )
        
        self.logger.info(f"联盟生成阶段完成: 有效联盟 {len(valid_coalitions)} 个，剪枝联盟 {len(pruned_coalitions)} 个")
        
        return {
            "success": True,
            "valid_coalitions": valid_coalitions,
            "pruned_coalitions": pruned_coalitions,
            "coalition_analysis": coalition_analysis,
            "generation_stats": self.coalition_manager.get_stats()
        }

    def _run_weekly_coalition_simulation_phase(self,
                                             target_agents: List[str],
                                             agents: Dict[str, Any],
                                             max_coalitions: Optional[int] = None) -> Dict[str, Any]:
        """
        运行周期性联盟生成与交易模拟阶段

        这个方法实现了正确的周期性联盟形成逻辑：
        1. 计算总的交易周数
        2. 对每一周：
           a. 生成该周的联盟
           b. 运行该周的交易模拟
           c. 计算智能体贡献度
           d. 决定是否需要优化

        参数:
            target_agents: 目标智能体列表
            agents: 智能体实例字典
            max_coalitions: 最大模拟联盟数量

        返回:
            周期性模拟阶段的执行结果
        """
        self.logger.info("开始周期性联盟生成与交易模拟阶段...")

        # 计算总的交易周数
        actual_simulation_days = self._calculate_actual_simulation_days()
        trading_days_per_week = 5
        total_weeks = (actual_simulation_days + trading_days_per_week - 1) // trading_days_per_week

        self.logger.info(f"总交易天数: {actual_simulation_days}, 计划交易周数: {total_weeks}")

        # 存储每周的结果
        weekly_results = []
        all_coalition_values = {}
        all_coalition_daily_returns = {}
        weekly_shapley_results = []

        # 周期性评估统计
        weekly_stats = {
            "total_weeks": total_weeks,
            "completed_weeks": 0,
            "coalition_generations": 0,
            "simulations_per_week": [],
            "shapley_calculations": 0,
            "optimization_triggers": 0
        }

        for week in range(total_weeks):
            week_start_day = week * trading_days_per_week
            week_end_day = min(week_start_day + trading_days_per_week, actual_simulation_days)
            actual_week_days = week_end_day - week_start_day

            self.logger.info("=" * 60)
            self.logger.info(f"第 {week + 1} 周交易 (第 {week_start_day + 1}-{week_end_day} 天)")
            self.logger.info("=" * 60)

            try:
                # 步骤1: 生成本周的联盟
                self.logger.info(f"步骤1: 生成第 {week + 1} 周的联盟")
                coalition_result = self._run_coalition_generation_phase(target_agents)
                weekly_stats["coalition_generations"] += 1

                # 步骤2: 运行本周的交易模拟（限制模拟天数为本周天数）
                self.logger.info(f"步骤2: 运行第 {week + 1} 周的交易模拟 ({actual_week_days} 天)")

                # 临时修改配置中的模拟天数
                original_simulation_days = self.config.get("simulation_days")
                self.config["simulation_days"] = actual_week_days

                week_simulation_result = self._run_trading_simulation_phase(
                    coalition_result["valid_coalitions"],
                    agents,
                    max_coalitions
                )

                # 恢复原始配置
                self.config["simulation_days"] = original_simulation_days

                weekly_stats["simulations_per_week"].append(
                    week_simulation_result["simulation_stats"]["successful_simulations"]
                )

                # 步骤3: 计算本周的Shapley值
                self.logger.info(f"步骤3: 计算第 {week + 1} 周的Shapley值")
                week_shapley_result = self._run_shapley_calculation_phase(
                    target_agents,
                    week_simulation_result["coalition_values"]
                )
                weekly_stats["shapley_calculations"] += 1

                # 步骤4: 分析智能体贡献度并决定是否需要优化
                self.logger.info(f"步骤4: 分析第 {week + 1} 周的智能体贡献度")
                contribution_analysis = self._analyze_weekly_contributions(
                    week_shapley_result, week + 1
                )

                # 检查是否需要触发优化
                if contribution_analysis.get("needs_optimization", False):
                    self.logger.info(f"第 {week + 1} 周检测到需要优化的智能体")
                    weekly_stats["optimization_triggers"] += 1

                    # 这里可以触发OPRO优化（如果启用）
                    if self.enable_opro:
                        optimization_result = self._trigger_weekly_optimization(
                            contribution_analysis["low_performing_agents"]
                        )
                        contribution_analysis["optimization_result"] = optimization_result

                # 存储本周结果
                week_result = {
                    "week": week + 1,
                    "days_range": f"{week_start_day + 1}-{week_end_day}",
                    "actual_days": actual_week_days,
                    "coalition_result": coalition_result,
                    "simulation_result": week_simulation_result,
                    "shapley_result": week_shapley_result,
                    "contribution_analysis": contribution_analysis
                }

                weekly_results.append(week_result)
                weekly_stats["completed_weeks"] += 1

                # 累积联盟值和每日收益（用于最终计算）
                for coalition, value in week_simulation_result["coalition_values"].items():
                    if coalition not in all_coalition_values:
                        all_coalition_values[coalition] = []
                    all_coalition_values[coalition].append(value)

                for coalition, returns in week_simulation_result["coalition_daily_returns"].items():
                    if coalition not in all_coalition_daily_returns:
                        all_coalition_daily_returns[coalition] = []
                    all_coalition_daily_returns[coalition].extend(returns)

                weekly_shapley_results.append(week_shapley_result)

                self.logger.info(f"第 {week + 1} 周完成: "
                               f"联盟 {len(coalition_result['valid_coalitions'])} 个, "
                               f"模拟 {week_simulation_result['simulation_stats']['successful_simulations']} 个")

            except Exception as e:
                self.logger.error(f"第 {week + 1} 周执行失败: {e}")
                # 继续下一周的处理
                continue

        # 计算最终的联盟平均值
        final_coalition_values = {}
        for coalition, values in all_coalition_values.items():
            final_coalition_values[coalition] = sum(values) / len(values) if values else 0.0

        self.logger.info(f"周期性联盟生成与交易模拟阶段完成: "
                       f"完成 {weekly_stats['completed_weeks']}/{total_weeks} 周")

        return {
            "success": True,
            "weekly_results": weekly_results,
            "final_coalition_values": final_coalition_values,
            "all_coalition_daily_returns": all_coalition_daily_returns,
            "weekly_shapley_results": weekly_shapley_results,
            "weekly_stats": weekly_stats,
            "total_weeks": total_weeks,
            "completed_weeks": weekly_stats["completed_weeks"]
        }

    def _analyze_weekly_contributions(self,
                                    shapley_result: Dict[str, Any],
                                    week_number: int) -> Dict[str, Any]:
        """
        分析每周的智能体贡献度

        参数:
            shapley_result: 本周的Shapley值计算结果
            week_number: 周数

        返回:
            贡献度分析结果
        """
        try:
            shapley_values = shapley_result.get("shapley_values", {})

            if not shapley_values:
                return {
                    "week": week_number,
                    "needs_optimization": False,
                    "analysis_error": "无Shapley值数据"
                }

            # 计算贡献度统计
            values = list(shapley_values.values())
            mean_contribution = sum(values) / len(values) if values else 0.0

            # 识别低性能智能体（贡献度低于平均值的80%）
            low_performance_threshold = mean_contribution * 0.8
            low_performing_agents = []
            high_performing_agents = []

            for agent_id, contribution in shapley_values.items():
                if contribution < low_performance_threshold:
                    low_performing_agents.append({
                        "agent_id": agent_id,
                        "contribution": contribution,
                        "performance_ratio": contribution / mean_contribution if mean_contribution > 0 else 0.0
                    })
                else:
                    high_performing_agents.append({
                        "agent_id": agent_id,
                        "contribution": contribution,
                        "performance_ratio": contribution / mean_contribution if mean_contribution > 0 else 1.0
                    })

            # 决定是否需要优化
            needs_optimization = len(low_performing_agents) > 0

            analysis_result = {
                "week": week_number,
                "needs_optimization": needs_optimization,
                "mean_contribution": mean_contribution,
                "low_performance_threshold": low_performance_threshold,
                "low_performing_agents": low_performing_agents,
                "high_performing_agents": high_performing_agents,
                "total_agents": len(shapley_values),
                "low_performing_count": len(low_performing_agents),
                "optimization_priority": "high" if len(low_performing_agents) > len(shapley_values) // 2 else "medium"
            }

            if needs_optimization:
                self.logger.info(f"第 {week_number} 周发现 {len(low_performing_agents)} 个低性能智能体需要优化")
                for agent_info in low_performing_agents:
                    self.logger.info(f"  - {agent_info['agent_id']}: 贡献度 {agent_info['contribution']:.6f} "
                                   f"(相对性能 {agent_info['performance_ratio']:.2f})")
            else:
                self.logger.info(f"第 {week_number} 周所有智能体性能良好，无需优化")

            return analysis_result

        except Exception as e:
            self.logger.error(f"分析第 {week_number} 周贡献度失败: {e}")
            return {
                "week": week_number,
                "needs_optimization": False,
                "analysis_error": str(e)
            }

    def _trigger_weekly_optimization(self, low_performing_agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        触发周期性优化

        参数:
            low_performing_agents: 低性能智能体列表

        返回:
            优化结果
        """
        if not self.enable_opro or not self.opro_optimizer:
            return {
                "success": False,
                "error": "OPRO优化未启用或优化器未配置"
            }

        try:
            # 提取需要优化的智能体ID
            agent_ids = [agent["agent_id"] for agent in low_performing_agents]

            self.logger.info(f"触发周期性优化: {len(agent_ids)} 个智能体")

            # 获取当前提示词
            current_prompts = self._get_current_prompts(agent_ids)

            # 运行批量优化
            optimization_result = self.opro_optimizer.optimize_all_agents(
                agent_ids=agent_ids,
                current_prompts=current_prompts
            )

            if optimization_result.get("success", False):
                self.logger.info(f"周期性优化完成: {optimization_result.get('successful_optimizations', 0)} 个智能体优化成功")
            else:
                self.logger.warning(f"周期性优化失败: {optimization_result.get('error', '未知错误')}")

            return optimization_result

        except Exception as e:
            self.logger.error(f"触发周期性优化失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _run_trading_simulation_phase(self, 
                                    valid_coalitions: Set[frozenset], 
                                    agents: Dict[str, Any],
                                    max_coalitions: Optional[int] = None) -> Dict[str, Any]:
        """
        运行交易模拟阶段（支持并发执行）
        
        参数:
            valid_coalitions: 有效联盟集合
            agents: 智能体实例字典
            max_coalitions: 最大模拟联盟数量
            
        返回:
            交易模拟阶段的执行结果
        """
        self.logger.info("开始交易模拟阶段...")
        
        # 限制模拟的联盟数量（如果指定）
        coalitions_to_simulate = list(valid_coalitions)
        if max_coalitions and len(coalitions_to_simulate) > max_coalitions:
            self.logger.info(f"限制模拟联盟数量: {len(coalitions_to_simulate)} -> {max_coalitions}")
            coalitions_to_simulate = coalitions_to_simulate[:max_coalitions]
        
        # 选择执行模式：并发或串行
        if self.enable_concurrent_execution and len(coalitions_to_simulate) > 5:
            return self._run_concurrent_trading_simulation(coalitions_to_simulate, agents)
        else:
            return self._run_serial_trading_simulation(coalitions_to_simulate, agents)

    def _run_concurrent_trading_simulation(self, 
                                         coalitions_to_simulate: List[frozenset],
                                         agents: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行并发交易模拟
        
        参数:
            coalitions_to_simulate: 要模拟的联盟列表
            agents: 智能体实例字典
            
        返回:
            交易模拟阶段的执行结果
        """
        self.logger.info(f"启用并发模拟：{len(coalitions_to_simulate)} 个联盟，最大并发数：{self.max_concurrent_api_calls}")
        
        start_time = time.time()
        coalition_values = {}
        coalition_daily_returns = {}
        simulation_stats = {
            "total_coalitions": len(coalitions_to_simulate),
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_simulation_time": 0.0,
            "concurrent_execution": True
        }
        
        # 使用线程锁保护共享资源
        results_lock = threading.Lock()
        stats_lock = threading.Lock()
        
        def simulate_single_coalition(coalition: frozenset) -> Dict[str, Any]:
            """单个联盟的模拟任务"""
            coalition_set = set(coalition)
            task_start_time = time.time()
            
            try:
                self.logger.debug(f"🔄 开始并发模拟联盟: {coalition_set}")
                
                simulation_result = self.trading_simulator.run_simulation_for_coalition(
                    coalition_set,
                    self.analysis_cache,
                    agents,
                    self.config.get("simulation_days")
                )
                
                task_time = time.time() - task_start_time
                
                # 处理返回结果
                if isinstance(simulation_result, dict):
                    sharpe_ratio = simulation_result["sharpe_ratio"]
                    daily_returns = simulation_result["daily_returns"]
                else:
                    sharpe_ratio = simulation_result
                    daily_returns = []
                
                # 线程安全地更新结果
                with results_lock:
                    coalition_values[coalition] = sharpe_ratio
                    coalition_daily_returns[coalition] = daily_returns
                
                with stats_lock:
                    simulation_stats["successful_simulations"] += 1
                    self._concurrent_stats["successful_concurrent_tasks"] += 1
                
                self.logger.debug(f"[SUCCESS] 联盟 {coalition_set} 模拟完成: {sharpe_ratio:.4f} ({task_time:.2f}s)")
                return {"success": True, "coalition": coalition, "sharpe_ratio": sharpe_ratio}
                
            except Exception as e:
                task_time = time.time() - task_start_time
                self.logger.error(f"[ERROR] 联盟 {coalition_set} 模拟失败: {e} ({task_time:.2f}s)")
                
                # 线程安全地更新错误结果
                with results_lock:
                    coalition_values[coalition] = 0.0
                    coalition_daily_returns[coalition] = []
                
                with stats_lock:
                    simulation_stats["failed_simulations"] += 1
                    self._concurrent_stats["failed_concurrent_tasks"] += 1
                
                return {"success": False, "coalition": coalition, "error": str(e)}
        
        # 执行并发模拟
        completed_tasks = 0
        total_tasks = len(coalitions_to_simulate)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent_api_calls) as executor:
            # 提交所有任务
            future_to_coalition = {
                executor.submit(simulate_single_coalition, coalition): coalition 
                for coalition in coalitions_to_simulate
            }
            
            self._concurrent_stats["total_concurrent_tasks"] += len(future_to_coalition)
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_coalition):
                completed_tasks += 1
                coalition = future_to_coalition[future]
                
                try:
                    result = future.result()
                    if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                        self.logger.info(f"📊 并发进度: {completed_tasks}/{total_tasks} ({completed_tasks/total_tasks*100:.1f}%)")
                except Exception as e:
                    self.logger.error(f"任务执行异常 {set(coalition)}: {e}")
        
        # 汇总统计信息
        total_time = time.time() - start_time
        simulation_stats["total_simulation_time"] = total_time
        self._concurrent_stats["concurrent_execution_time"] += total_time
        
        self.logger.info(f"[SUCCESS] 并发交易模拟完成: 成功 {simulation_stats['successful_simulations']} 个，"
                        f"失败 {simulation_stats['failed_simulations']} 个，总耗时 {total_time:.2f}s")
        
        return {
            "success": True,
            "coalition_values": coalition_values,
            "coalition_daily_returns": coalition_daily_returns,
            "simulation_stats": simulation_stats,
            "concurrent_stats": self._concurrent_stats.copy()
        }

    def _run_serial_trading_simulation(self, 
                                     coalitions_to_simulate: List[frozenset],
                                     agents: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行串行交易模拟（原有逻辑）
        
        参数:
            coalitions_to_simulate: 要模拟的联盟列表
            agents: 智能体实例字典
            
        返回:
            交易模拟阶段的执行结果
        """
        self.logger.info(f"使用串行模拟：{len(coalitions_to_simulate)} 个联盟")
        
        coalition_values = {}
        coalition_daily_returns = {}
        simulation_stats = {
            "total_coalitions": len(coalitions_to_simulate),
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_simulation_time": 0.0,
            "concurrent_execution": False
        }

        simulation_days = self.config.get("simulation_days")

        for i, coalition in enumerate(coalitions_to_simulate):
            coalition_set = set(coalition)
            self.logger.info(f"模拟联盟 {i+1}/{len(coalitions_to_simulate)}: {coalition_set}")

            try:
                simulation_result = self.trading_simulator.run_simulation_for_coalition(
                    coalition_set,
                    self.analysis_cache,
                    agents,
                    simulation_days
                )

                # 处理新的返回格式
                if isinstance(simulation_result, dict):
                    coalition_values[coalition] = simulation_result["sharpe_ratio"]
                    coalition_daily_returns[coalition] = simulation_result["daily_returns"]
                else:
                    # 向后兼容：如果返回的是单个值
                    coalition_values[coalition] = simulation_result
                    coalition_daily_returns[coalition] = []

                simulation_stats["successful_simulations"] += 1

            except Exception as e:
                self.logger.error(f"联盟 {coalition_set} 模拟失败: {e}")
                coalition_values[coalition] = 0.0
                coalition_daily_returns[coalition] = []
                simulation_stats["failed_simulations"] += 1
        
        # 获取模拟器统计信息
        simulator_stats = self.trading_simulator.get_stats()
        simulation_stats["total_simulation_time"] = simulator_stats.get("total_simulation_time", 0.0)
        
        self.logger.info(f"串行交易模拟完成: 成功 {simulation_stats['successful_simulations']} 个，失败 {simulation_stats['failed_simulations']} 个")
        
        return {
            "success": True,
            "coalition_values": coalition_values,
            "coalition_daily_returns": coalition_daily_returns,
            "simulation_stats": simulation_stats,
            "simulator_stats": simulator_stats
        }
    
    def _run_shapley_calculation_phase(self, 
                                     target_agents: List[str], 
                                     coalition_values: Dict[frozenset, float]) -> Dict[str, Any]:
        """
        运行Shapley值计算阶段
        
        参数:
            target_agents: 目标智能体列表
            coalition_values: 联盟特征函数值字典
            
        返回:
            Shapley值计算阶段的执行结果
        """
        self.logger.info("开始Shapley值计算阶段...")
        
        # 计算Shapley值
        shapley_values = self.shapley_calculator.calculate(target_agents, coalition_values)
        
        # 分析Shapley值
        shapley_analysis = self.shapley_calculator.analyze_shapley_values(shapley_values)
        
        # 获取计算统计信息
        calculation_stats = self.shapley_calculator.get_stats()
        
        self.logger.info(f"Shapley值计算阶段完成: 计算了 {len(shapley_values)} 个智能体的贡献度")
        
        return {
            "success": True,
            "shapley_values": shapley_values,
            "shapley_analysis": shapley_analysis,
            "calculation_stats": calculation_stats
        }

    def _run_periodic_shapley_calculation_phase(self,
                                              target_agents: List[str],
                                              coalition_daily_returns: Dict[frozenset, List[float]],
                                              total_simulation_days: int) -> Dict[str, Any]:
        """
        运行周期性Shapley值计算阶段

        按5个交易日为一周的周期，对每个周期的收益数据进行Shapley值计算。

        参数:
            target_agents: 目标智能体列表
            coalition_daily_returns: 所有联盟的每日收益数据字典
            total_simulation_days: 总模拟天数

        返回:
            周期性Shapley值计算阶段的执行结果
        """
        self.logger.info("开始周期性Shapley值计算阶段...")

        # 计算周期数（每5个交易日为一周）
        trading_days_per_week = 5
        
        # 根据实际数据确定模拟天数
        actual_data_days = 0
        if coalition_daily_returns:
            # 找到实际数据的最大长度
            max_data_length = max(len(returns) for returns in coalition_daily_returns.values() if returns)
            actual_data_days = max_data_length
            
        # 如果没有数据或数据长度为0，使用配置的天数
        if actual_data_days == 0:
            if total_simulation_days is None or total_simulation_days <= 0:
                self.logger.warning("total_simulation_days为None或无效，使用默认值20")
                actual_data_days = 20
            else:
                actual_data_days = total_simulation_days
            
        total_weeks = (actual_data_days + trading_days_per_week - 1) // trading_days_per_week

        self.logger.info(f"实际数据天数: {actual_data_days}, 计算周期数: {total_weeks}")
        
        # 如果实际数据天数与配置不符，给出提示
        if total_simulation_days and actual_data_days != total_simulation_days:
            self.logger.info(f"注意: 配置的模拟天数({total_simulation_days})与实际数据天数({actual_data_days})不一致，使用实际数据天数")

        weekly_shapley_results = []

        for week in range(total_weeks):
            start_day = week * trading_days_per_week
            end_day = min(start_day + trading_days_per_week, actual_data_days)

            self.logger.info(f"计算第 {week + 1} 周 (第 {start_day + 1}-{end_day} 天) 的Shapley值")

            # 为当前周期构建联盟特征函数值
            weekly_coalition_values = {}

            for coalition, daily_returns in coalition_daily_returns.items():
                if len(daily_returns) >= end_day:
                    # 提取当前周期的收益数据
                    week_returns = daily_returns[start_day:end_day]

                    # 计算当前周期的夏普比率
                    weekly_sharpe = self._calculate_weekly_sharpe_ratio(week_returns)
                    weekly_coalition_values[coalition] = weekly_sharpe
                else:
                    # 如果数据不足，赋值为0
                    weekly_coalition_values[coalition] = 0.0

            # 计算当前周期的Shapley值
            try:
                weekly_shapley_values = self.shapley_calculator.calculate(
                    target_agents, weekly_coalition_values
                )

                weekly_result = {
                    "week": week + 1,
                    "trading_days": f"{start_day + 1}-{end_day}",
                    "shapley_values": weekly_shapley_values,
                    "coalition_values": weekly_coalition_values,
                    "success": True
                }

                # 打印当前周期结果
                self.logger.info(f"第 {week + 1} 周Shapley值计算完成:")
                for agent, value in weekly_shapley_values.items():
                    self.logger.info(f"  {agent}: {value:.6f}")

            except Exception as e:
                self.logger.error(f"第 {week + 1} 周Shapley值计算失败: {e}")
                weekly_result = {
                    "week": week + 1,
                    "trading_days": f"{start_day + 1}-{end_day}",
                    "shapley_values": {agent: 0.0 for agent in target_agents},
                    "coalition_values": weekly_coalition_values,
                    "success": False,
                    "error": str(e)
                }

            weekly_shapley_results.append(weekly_result)

        # 保存周期性结果到文件
        self._save_periodic_shapley_results(weekly_shapley_results)

        # 打印周期性结果汇总
        self._log_periodic_shapley_summary(weekly_shapley_results, target_agents)

        self.logger.info(f"周期性Shapley值计算阶段完成: 计算了 {total_weeks} 个周期")

        return {
            "success": True,
            "total_weeks": total_weeks,
            "weekly_results": weekly_shapley_results,
            "trading_days_per_week": trading_days_per_week
        }

    def _calculate_weekly_sharpe_ratio(self, returns: List[float]) -> float:
        """
        计算周期收益的夏普比率

        参数:
            returns: 收益率列表

        返回:
            夏普比率
        """
        if not returns or len(returns) == 0:
            return 0.0

        import numpy as np

        returns_array = np.array(returns)

        # 计算平均收益率
        mean_return = np.mean(returns_array)

        # 计算收益率标准差
        std_return = np.std(returns_array, ddof=1) if len(returns_array) > 1 else 0.0

        # 计算夏普比率（假设无风险利率为0）
        if std_return == 0:
            return 0.0

        sharpe_ratio = mean_return / std_return

        # 年化处理（假设252个交易日）
        sharpe_ratio_annualized = sharpe_ratio * np.sqrt(252)

        return sharpe_ratio_annualized

    def _generate_returns_analysis(self, 
                                 simulation_result: Dict[str, Any], 
                                 shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """
        生成收益率分析报告
        
        参数:
            simulation_result: 交易模拟结果
            shapley_values: Shapley值字典
            
        返回:
            收益率分析报告
        """
        try:
            from analysis.returns_analyzer import ReturnsAnalyzer
            
            # 提取联盟收益数据
            coalition_values = simulation_result.get("coalition_values", {})
            coalition_daily_returns = simulation_result.get("coalition_daily_returns", {})
            
            # 构建收益率分析器需要的数据格式
            coalition_results = {}
            for coalition, sharpe_ratio in coalition_values.items():
                coalition_id = str(coalition)  # 转换为字符串ID
                daily_returns = coalition_daily_returns.get(coalition, [])
                
                coalition_results[coalition_id] = {
                    "daily_returns": daily_returns,
                    "sharpe_ratio": sharpe_ratio,
                    "simulation_time": 0.0  # 从模拟统计中获取
                }
            
            if not coalition_results:
                self.logger.warning("没有联盟收益数据可供分析")
                return {"error": "没有收益数据", "analysis_completed": False}
            
            # 创建收益率分析器
            analyzer = ReturnsAnalyzer(logger=self.logger)
            
            # 执行分析
            analysis_results = analyzer.analyze_coalition_returns(
                coalition_results, shapley_values
            )
            
            # 生成可视化图表
            plot_paths = analyzer.generate_visualization(save_plots=True)
            
            # 生成性能报告
            performance_report = analyzer.generate_performance_report(save_report=True)
            
            # 整理最终结果
            returns_analysis = {
                "analysis_completed": True,
                "total_coalitions_analyzed": len(coalition_results),
                "analysis_results": analysis_results,
                "plot_paths": plot_paths,
                "performance_report_path": performance_report.get("report_file", ""),
                "best_coalition": self._extract_best_coalition_info(analysis_results),
                "summary_statistics": {
                    "avg_sharpe_ratio": analysis_results.get("overall_statistics", {}).get("avg_sharpe_ratio", 0),
                    "best_sharpe_ratio": analysis_results.get("overall_statistics", {}).get("best_sharpe_ratio", 0),
                    "strategy_diversification": analysis_results.get("overall_statistics", {}).get("strategy_diversification", 0)
                }
            }
            
            self.logger.info(f"收益率分析完成: 分析了 {len(coalition_results)} 个联盟，生成了 {len(plot_paths)} 个图表")
            
            return returns_analysis
            
        except ImportError:
            self.logger.warning("收益率分析器模块不可用，跳过收益率分析")
            return {"error": "模块不可用", "analysis_completed": False}
            
        except Exception as e:
            self.logger.error(f"收益率分析失败: {e}")
            return {"error": str(e), "analysis_completed": False}
    
    def _extract_best_coalition_info(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取最佳表现联盟的信息
        
        参数:
            analysis_results: 分析结果
            
        返回:
            最佳联盟信息
        """
        overall_stats = analysis_results.get("overall_statistics", {})
        
        best_coalition_info = {
            "coalition_id": overall_stats.get("best_sharpe_coalition", ""),
            "sharpe_ratio": overall_stats.get("best_sharpe_ratio", 0),
            "annualized_return": overall_stats.get("best_return", 0),
            "performance_rank": "最佳"
        }
        
        # 如果有最佳联盟，获取更详细的信息
        best_coalition_id = best_coalition_info["coalition_id"]
        if best_coalition_id and best_coalition_id in analysis_results:
            coalition_detail = analysis_results[best_coalition_id]
            basic_stats = coalition_detail.get("basic_statistics", {})
            
            best_coalition_info.update({
                "total_return": basic_stats.get("total_return", 0),
                "volatility": basic_stats.get("volatility", 0),
                "max_drawdown": abs(coalition_detail.get("drawdown_analysis", {}).get("max_drawdown", 0)),
                "win_rate": basic_stats.get("win_rate", 0),
                "members": coalition_detail.get("coalition_members", [])
            })
        
        return best_coalition_info


    def _compile_final_result(self,
                            cache_result: Dict[str, Any],
                            coalition_result: Dict[str, Any],
                            simulation_result: Dict[str, Any],
                            shapley_result: Dict[str, Any],
                            periodic_shapley_result: Dict[str, Any],
                            total_time: float) -> Dict[str, Any]:
        """
        汇总最终结果

        参数:
            cache_result: 缓存阶段结果
            coalition_result: 联盟生成阶段结果
            simulation_result: 模拟阶段结果
            shapley_result: Shapley计算阶段结果
            periodic_shapley_result: 周期性Shapley值计算结果
            total_time: 总执行时间

        返回:
            完整的评估结果字典
        """
        # 生成收益率分析报告
        returns_analysis = self._generate_returns_analysis(
            simulation_result, shapley_result["shapley_values"]
        )
        
        return {
            "success": True,
            "execution_time": total_time,
            "shapley_values": shapley_result["shapley_values"],
            "shapley_analysis": shapley_result["shapley_analysis"],
            "periodic_shapley_results": periodic_shapley_result,  # 新增：周期性结果
            "returns_analysis": returns_analysis,  # 新增：收益率分析
            "phase_results": {
                "analysis_caching": cache_result,
                "coalition_generation": coalition_result,
                "trading_simulation": simulation_result,
                "periodic_shapley_calculation": periodic_shapley_result,  # 新增
                "shapley_calculation": shapley_result
            },
            "summary": {
                "total_agents": len(shapley_result["shapley_values"]),
                "total_coalitions_generated": coalition_result["coalition_analysis"]["total_coalitions"],
                "coalitions_simulated": simulation_result["simulation_stats"]["total_coalitions"],
                "simulation_success_rate": (
                    simulation_result["simulation_stats"]["successful_simulations"] /
                    max(simulation_result["simulation_stats"]["total_coalitions"], 1)
                ) * 100,
                "top_contributor": shapley_result["shapley_analysis"]["max_contributor"],
                "total_system_value": shapley_result["shapley_analysis"]["total_value"],
                "weekly_shapley_periods": periodic_shapley_result.get("total_weeks", 0),  # 新增
                "best_performing_coalition": returns_analysis.get("best_coalition", {})  # 新增
            }
        }

    def _compile_weekly_final_result(self,
                                   cache_result: Dict[str, Any],
                                   weekly_simulation_result: Dict[str, Any],
                                   shapley_result: Dict[str, Any],
                                   total_time: float) -> Dict[str, Any]:
        """
        汇总周期性评估的最终结果

        参数:
            cache_result: 缓存阶段结果
            weekly_simulation_result: 周期性模拟阶段结果
            shapley_result: 最终Shapley计算阶段结果
            total_time: 总执行时间

        返回:
            完整的评估结果字典
        """
        # 生成收益率分析报告
        returns_analysis = self._generate_weekly_returns_analysis(
            weekly_simulation_result, shapley_result["shapley_values"]
        )

        # 计算周期性统计
        weekly_stats = weekly_simulation_result.get("weekly_stats", {})

        # 保存Shapley计算结果到存储管理器
        try:
            coalition_values = weekly_simulation_result.get("final_coalition_values", {})
            coalition_values_frozenset = {frozenset(k.split("_")): v for k, v in coalition_values.items()}

            calculation_metadata = {
                "total_coalitions": len(coalition_values),
                "calculation_method": "standard_shapley",
                "weekly_evaluation_enabled": True,
                "total_weeks": weekly_stats.get("total_weeks", 0),
                "completed_weeks": weekly_stats.get("completed_weeks", 0)
            }

            self.storage_manager.save_shapley_calculation_result(
                shapley_values=shapley_result["shapley_values"],
                coalition_values=coalition_values_frozenset,
                calculation_metadata=calculation_metadata
            )

            # 完成实验
            experiment_summary = {
                "total_agents": len(shapley_result["shapley_values"]),
                "total_coalitions": len(coalition_values),
                "execution_time": total_time,
                "success": True
            }
            self.storage_manager.finish_experiment(experiment_summary)

        except Exception as e:
            self.logger.warning(f"保存Shapley结果到存储管理器失败: {e}")

        return {
            "success": True,
            "execution_time": total_time,
            "shapley_values": shapley_result["shapley_values"],
            "shapley_analysis": shapley_result["shapley_analysis"],
            "weekly_simulation_results": weekly_simulation_result["weekly_results"],
            "weekly_shapley_results": weekly_simulation_result["weekly_shapley_results"],
            "returns_analysis": returns_analysis,
            "phase_results": {
                "analysis_caching": cache_result,
                "weekly_coalition_simulation": weekly_simulation_result,
                "final_shapley_calculation": shapley_result
            },
            "summary": {
                "total_agents": len(shapley_result["shapley_values"]),
                "total_weeks": weekly_stats.get("total_weeks", 0),
                "completed_weeks": weekly_stats.get("completed_weeks", 0),
                "coalition_generations": weekly_stats.get("coalition_generations", 0),
                "shapley_calculations": weekly_stats.get("shapley_calculations", 0),
                "optimization_triggers": weekly_stats.get("optimization_triggers", 0),
                "top_contributor": shapley_result["shapley_analysis"]["max_contributor"],
                "total_system_value": shapley_result["shapley_analysis"]["total_value"],
                "best_performing_coalition": returns_analysis.get("best_coalition", {}),
                "weekly_completion_rate": (
                    weekly_stats.get("completed_weeks", 0) /
                    max(weekly_stats.get("total_weeks", 1), 1)
                ) * 100
            }
        }

    def _generate_weekly_returns_analysis(self,
                                        weekly_simulation_result: Dict[str, Any],
                                        shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """
        生成周期性收益率分析

        参数:
            weekly_simulation_result: 周期性模拟结果
            shapley_values: Shapley值

        返回:
            收益率分析结果
        """
        try:
            final_coalition_values = weekly_simulation_result.get("final_coalition_values", {})

            if not final_coalition_values:
                return {"error": "无联盟收益数据"}

            # 找到最佳联盟
            best_coalition = max(final_coalition_values.items(), key=lambda x: x[1])
            worst_coalition = min(final_coalition_values.items(), key=lambda x: x[1])

            # 计算统计信息
            values = list(final_coalition_values.values())
            mean_value = sum(values) / len(values) if values else 0.0

            return {
                "best_coalition": {
                    "coalition": list(best_coalition[0]),
                    "value": best_coalition[1]
                },
                "worst_coalition": {
                    "coalition": list(worst_coalition[0]),
                    "value": worst_coalition[1]
                },
                "mean_coalition_value": mean_value,
                "total_coalitions_analyzed": len(final_coalition_values),
                "value_range": best_coalition[1] - worst_coalition[1]
            }

        except Exception as e:
            return {"error": f"生成收益率分析失败: {e}"}

    def _create_mock_state(self) -> Dict[str, Any]:
        """
        创建真实状态（用于智能体执行）

        修复：使用真实的StockTradingEnv状态而不是模拟数据

        返回:
            真实的环境状态字典
        """
        # 创建真实的交易环境来获取状态
        try:
            from stock_trading_env import StockTradingEnv

            # 创建环境配置
            env_config = self.config.copy()

            # 创建交易环境
            env = StockTradingEnv(env_config)

            # 获取真实状态
            real_state, _ = env.reset()

            self.logger.info(f"✅ 使用真实环境状态，包含 {len(real_state.get('news_history', {}))} 个日期的新闻数据")

            # 只添加分析期间信息，不添加模拟的分析输出
            # 让代理基于真实数据进行分析，而不是看到预设的模拟结果
            real_state.update({
                "analysis_period": {
                    "start_date": self.config.get("start_date"),
                    "end_date": self.config.get("end_date")
                }
            })

            return real_state

        except Exception as e:
            self.logger.warning(f"无法创建真实环境状态，回退到模拟状态: {e}")

            # 回退到原始的模拟状态逻辑
            mock_naa_analysis = self._get_mock_analysis_data("NAA")
            mock_taa_analysis = self._get_mock_analysis_data("TAA")
            mock_faa_analysis = self._get_mock_analysis_data("FAA")

            mock_boa_outlook = self._get_mock_analysis_data("BOA")
            mock_beoa_outlook = self._get_mock_analysis_data("BeOA")
            mock_noa_outlook = self._get_mock_analysis_data("NOA")

            analysis_date_str = self.config.get("start_date", datetime.now().strftime("%Y-%m-%d"))

            return {
                "date": analysis_date_str,
                "current_date": analysis_date_str,
                "analysis_period": {
                    "start_date": self.config.get("start_date"),
                    "end_date": self.config.get("end_date")
                },
                "cash": self.config.get("starting_cash", 1000000),
                "positions": {},
                "position_values": {},
                "price_history": {},
                "news_history": {},
                "fundamental_data": {},
                "analyst_outputs": {
                    "NAA": mock_naa_analysis,
                    "TAA": mock_taa_analysis,
                    "FAA": mock_faa_analysis,
                },
                "outlook_outputs": {
                    "BOA": mock_boa_outlook,
                    "BeOA": mock_beoa_outlook,
                    "NOA": mock_noa_outlook,
                }
            }

    def _get_mock_analysis_data(self, agent_id: str) -> Dict[str, Any]:
        """
        获取模拟的分析数据

        参数:
            agent_id: 智能体ID

        返回:
            模拟的分析结果
        """
        if agent_id == "NAA":
            return {
                "sentiment": 0.0,
                "summary": "无新闻数据可用",
                "confidence": 0.5,
                "source": "mock_data"
            }
        elif agent_id == "TAA":
            return {
                "trend": "neutral",
                "indicators": {"RSI": 50, "MACD": 0},
                "confidence": 0.5,
                "source": "mock_data"
            }
        elif agent_id == "FAA":
            return {
                "valuation": "fair",
                "metrics": {"PE": 20, "PB": 2},
                "confidence": 0.5,
                "source": "mock_data"
            }
        elif agent_id == "BOA":
            return {
                "outlook": "bullish",
                "reasoning": "模拟看涨展望：基于积极新闻和技术指标。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "BeOA":
            return {
                "outlook": "bearish",
                "reasoning": "模拟看跌展望：基于负面基本面和下跌趋势。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "NOA":
            return {
                "outlook": "neutral",
                "reasoning": "模拟中性展望：市场信号混杂，无明显趋势。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "TRA":
            return {
                "action": "HOLD",
                "quantity": 0,
                "reasoning": "模拟交易决策：展望层意见不一，保持观望。",
                "confidence": 0.5,
                "source": "mock_data"
            }
        else:
            return {
                "output": "default",
                "confidence": 0.0,
                "source": "mock_data"
            }

    def _update_stats(self, execution_time: float, success: bool) -> None:
        """更新执行统计信息"""
        self._stats["total_runs"] += 1
        self._stats["last_run_time"] = execution_time
        self._stats["last_run"] = datetime.now()

        if success:
            self._stats["successful_runs"] += 1
        else:
            self._stats["failed_runs"] += 1

        # 计算平均执行时间
        if self._stats["total_runs"] > 0:
            total_time = (self._stats["average_run_time"] * (self._stats["total_runs"] - 1) +
                         execution_time)
            self._stats["average_run_time"] = total_time / self._stats["total_runs"]

    def get_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息

        返回:
            包含统计信息的字典
        """
        stats = self._stats.copy()
        if stats["total_runs"] > 0:
            stats["success_rate"] = (stats["successful_runs"] / stats["total_runs"]) * 100
        else:
            stats["success_rate"] = 0.0
        return stats

    def get_module_stats(self) -> Dict[str, Any]:
        """
        获取所有模块的统计信息

        返回:
            包含所有模块统计信息的字典
        """
        return {
            "assessor": self.get_stats(),
            "analysis_cache": self.analysis_cache.get_stats(),
            "coalition_manager": self.coalition_manager.get_stats(),
            "trading_simulator": self.trading_simulator.get_stats(),
            "shapley_calculator": self.shapley_calculator.get_stats(),
            "concurrent_execution": self._concurrent_stats.copy()
        }

    def set_concurrent_execution(self, enabled: bool, max_workers: Optional[int] = None) -> None:
        """
        设置并发执行参数
        
        参数:
            enabled: 是否启用并发执行
            max_workers: 最大并发工作数，如果为None则使用默认值
        """
        self.enable_concurrent_execution = enabled
        if max_workers is not None:
            self.max_concurrent_api_calls = max_workers
        
        self.logger.info(f"并发执行设置更新: 启用={enabled}, 最大并发数={self.max_concurrent_api_calls}")

    def get_concurrent_stats(self) -> Dict[str, Any]:
        """
        获取并发执行统计信息
        
        返回:
            并发执行统计字典
        """
        stats = self._concurrent_stats.copy()
        if stats["total_concurrent_tasks"] > 0:
            stats["concurrent_success_rate"] = (
                stats["successful_concurrent_tasks"] / stats["total_concurrent_tasks"]
            ) * 100
            stats["average_concurrent_task_time"] = (
                stats["concurrent_execution_time"] / stats["total_concurrent_tasks"]
            )
        else:
            stats["concurrent_success_rate"] = 0.0
            stats["average_concurrent_task_time"] = 0.0
        
        return stats

    def run_concurrent_daily_assessment(self, 
                                      date_range: List[str],
                                      agents: Optional[Dict[str, Any]] = None,
                                      target_agents: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        运行基于日期范围的并发评估
        
        针对每一天的子集进行并发处理，满足用户要求的并发控制策略。
        
        参数:
            date_range: 日期范围列表，例如 ["2025-01-01", "2025-01-02", "2025-01-03"]
            agents: 智能体实例字典
            target_agents: 目标智能体列表
            
        返回:
            并发评估结果字典
        """
        self.logger.info(f"开始并发日期评估: {len(date_range)} 天")
        
        daily_results = {}
        weekly_aggregated_results = {}
        
        for date in date_range:
            self.logger.info(f"📅 处理日期: {date}")
            
            # 更新配置为当天
            daily_config = self.config.copy()
            daily_config["start_date"] = date
            daily_config["end_date"] = date
            
            # 临时更新配置
            original_config = self.config
            self.config = daily_config
            
            try:
                # 重新初始化交易模拟器
                self.trading_simulator = TradingSimulator(
                    base_config=daily_config,
                    logger=self.logger
                )
                
                # 运行该日的评估
                daily_result = self.run(
                    agents=agents,
                    target_agents=target_agents,
                    max_coalitions=None  # 处理所有联盟
                )
                
                daily_results[date] = daily_result
                
                self.logger.info(f"[SUCCESS] 日期 {date} 处理完成")
                
            except Exception as e:
                self.logger.error(f"[ERROR] 日期 {date} 处理失败: {e}")
                daily_results[date] = {
                    "success": False,
                    "error": str(e),
                    "date": date
                }
            
            finally:
                # 恢复原配置
                self.config = original_config
                self.trading_simulator = TradingSimulator(
                    base_config=original_config,
                    logger=self.logger
                )
        
        # 计算周级汇总（每5个交易日为一周）
        weekly_aggregated_results = self._aggregate_daily_results_to_weekly(
            daily_results, target_agents or self.default_agents
        )
        
        return {
            "success": True,
            "daily_results": daily_results,
            "weekly_aggregated_results": weekly_aggregated_results,
            "total_days_processed": len(date_range),
            "concurrent_stats": self.get_concurrent_stats()
        }

    def _aggregate_daily_results_to_weekly(self, 
                                         daily_results: Dict[str, Dict[str, Any]],
                                         target_agents: List[str]) -> Dict[str, Any]:
        """
        将每日结果聚合为周级结果，然后计算Shapley值
        
        参数:
            daily_results: 每日结果字典
            target_agents: 目标智能体列表
            
        返回:
            周级聚合结果
        """
        self.logger.info("开始聚合每日结果为周级结果...")
        
        # 按周分组日期
        weekly_groups = defaultdict(list)
        sorted_dates = sorted(daily_results.keys())
        
        for i, date in enumerate(sorted_dates):
            week_num = i // 5  # 每5个交易日为一周
            weekly_groups[f"week_{week_num + 1}"].append(date)
        
        weekly_shapley_results = {}
        
        for week_id, dates in weekly_groups.items():
            self.logger.info(f"处理 {week_id}: {dates}")
            
            # 收集该周所有联盟的收益数据
            weekly_coalition_values = defaultdict(list)
            
            for date in dates:
                if date in daily_results and daily_results[date].get("success", False):
                    phase_results = daily_results[date].get("phase_results", {})
                    simulation_result = phase_results.get("trading_simulation", {})
                    coalition_values = simulation_result.get("coalition_values", {})
                    
                    for coalition, value in coalition_values.items():
                        weekly_coalition_values[coalition].append(value)
            
            # 计算该周的平均联盟值
            week_coalition_averages = {}
            for coalition, values in weekly_coalition_values.items():
                if values:
                    week_coalition_averages[coalition] = sum(values) / len(values)
                else:
                    week_coalition_averages[coalition] = 0.0
            
            # 计算该周的Shapley值
            if week_coalition_averages:
                try:
                    weekly_shapley_values = self.shapley_calculator.calculate(
                        target_agents, week_coalition_averages
                    )
                    
                    weekly_shapley_results[week_id] = {
                        "success": True,
                        "dates": dates,
                        "shapley_values": weekly_shapley_values,
                        "coalition_averages": week_coalition_averages
                    }
                    
                    self.logger.info(f"[SUCCESS] {week_id} Shapley值计算完成")
                    
                except Exception as e:
                    self.logger.error(f"[ERROR] {week_id} Shapley值计算失败: {e}")
                    weekly_shapley_results[week_id] = {
                        "success": False,
                        "error": str(e),
                        "dates": dates
                    }
            else:
                weekly_shapley_results[week_id] = {
                    "success": False,
                    "error": "无有效联盟数据",
                    "dates": dates
                }
        
        return weekly_shapley_results

    def _calculate_actual_simulation_days(self) -> int:
        """
        根据配置的日期范围计算实际的交易天数（使用真实日历检查）
        
        返回:
            实际的交易天数
        """
        try:
            import pandas as pd
            from datetime import datetime
            
            start_date = self.config.get("start_date")
            end_date = self.config.get("end_date")
            simulation_days = self.config.get("simulation_days")
            
            # 如果显式设置了simulation_days，使用该值
            if simulation_days is not None and simulation_days > 0:
                self.logger.info(f"使用配置的simulation_days: {simulation_days}")
                return simulation_days
            
            # 如果有日期范围，计算实际交易天数
            if start_date and end_date:
                actual_trading_days = self._count_trading_days(start_date, end_date)
                
                self.logger.info(f"根据真实日历计算: {start_date} 到 {end_date}, "
                               f"实际交易天数: {actual_trading_days}")
                return actual_trading_days
            
            # 默认值
            self.logger.warning("无法确定模拟天数，使用默认值20")
            return 20
            
        except Exception as e:
            self.logger.error(f"计算模拟天数时出错: {e}, 使用默认值20")
            return 20

    def _count_trading_days(self, start_date: str, end_date: str) -> int:
        """
        计算指定日期范围内的实际交易天数（排除周末和美股节假日）
        
        参数:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        返回:
            实际的交易天数
        """
        try:
            import pandas as pd
            from datetime import datetime, timedelta
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            # 生成日期范围
            date_range = pd.date_range(start=start_dt, end=end_dt, freq='D')
            
            trading_days = 0
            weekend_days = 0
            holiday_days = 0
            
            # 美股交易日历节假日数据库（2023-2026）
            us_holidays_db = {
                2023: [
                    '2023-01-02',  # New Year's Day (observed)
                    '2023-01-16',  # Martin Luther King Jr. Day
                    '2023-02-20',  # Presidents' Day
                    '2023-04-07',  # Good Friday
                    '2023-05-29',  # Memorial Day
                    '2023-06-19',  # Juneteenth (observed)
                    '2023-07-04',  # Independence Day
                    '2023-09-04',  # Labor Day
                    '2023-11-23',  # Thanksgiving
                    '2023-12-25',  # Christmas Day
                ],
                2024: [
                    '2024-01-01',  # New Year's Day
                    '2024-01-15',  # Martin Luther King Jr. Day
                    '2024-02-19',  # Presidents' Day
                    '2024-03-29',  # Good Friday
                    '2024-05-27',  # Memorial Day
                    '2024-06-19',  # Juneteenth
                    '2024-07-04',  # Independence Day
                    '2024-09-02',  # Labor Day
                    '2024-11-28',  # Thanksgiving
                    '2024-12-25',  # Christmas Day
                ],
                2025: [
                    '2025-01-01',  # New Year's Day
                    '2025-01-20',  # Martin Luther King Jr. Day
                    '2025-02-17',  # Presidents' Day
                    '2025-04-18',  # Good Friday
                    '2025-05-26',  # Memorial Day
                    '2025-06-19',  # Juneteenth
                    '2025-07-04',  # Independence Day
                    '2025-09-01',  # Labor Day
                    '2025-11-27',  # Thanksgiving
                    '2025-12-25',  # Christmas Day
                ],
                2026: [
                    '2026-01-01',  # New Year's Day
                    '2026-01-19',  # Martin Luther King Jr. Day
                    '2026-02-16',  # Presidents' Day
                    '2026-04-03',  # Good Friday
                    '2026-05-25',  # Memorial Day
                    '2026-06-19',  # Juneteenth
                    '2026-07-03',  # Independence Day (observed)
                    '2026-09-07',  # Labor Day
                    '2026-11-26',  # Thanksgiving
                    '2026-12-25',  # Christmas Day
                ]
            }
            
            # 提取当前日期范围内的节假日
            years_in_range = set(date.year for date in date_range)
            us_holidays_list = []
            for year in years_in_range:
                if year in us_holidays_db:
                    us_holidays_list.extend(us_holidays_db[year])
            
            # 转换为pandas日期格式
            us_holidays = pd.to_datetime(us_holidays_list)
            
            for date in date_range:
                date_str = date.strftime('%Y-%m-%d')
                
                # 检查是否为周末（周六=5, 周日=6）
                if date.weekday() >= 5:
                    weekend_days += 1
                    continue
                
                # 检查是否为美股节假日
                if date in us_holidays:
                    holiday_days += 1
                    continue
                
                # 其他情况为交易日
                trading_days += 1
            
            total_days = len(date_range)
            
            self.logger.info(f"📅 日期范围详细分析:")
            self.logger.info(f"  总天数: {total_days}")
            self.logger.info(f"  周末天数: {weekend_days}")
            self.logger.info(f"  节假日天数: {holiday_days}")
            self.logger.info(f"  交易天数: {trading_days}")
            
            # 确保至少有1个交易日
            return max(1, trading_days)
            
        except Exception as e:
            self.logger.error(f"真实日历计算失败: {e}")
            # 降级到估算方法
            return self._estimate_trading_days_fallback(start_date, end_date)

    def _estimate_trading_days_fallback(self, start_date: str, end_date: str) -> int:
        """
        降级的交易日估算方法（当真实日历检查失败时使用）
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            估算的交易天数
        """
        try:
            import pandas as pd
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            # 计算日历天数
            calendar_days = (end_dt - start_dt).days + 1
            
            # 估算交易天数（排除周末约71.4%，再排除节假日约68-70%）
            estimated_trading_days = int(calendar_days * 0.69)
            
            # 确保至少有1个交易日
            actual_days = max(1, estimated_trading_days)
            
            self.logger.info(f"降级估算: 日历天数 {calendar_days}, 估算交易天数 {actual_days}")
            return actual_days
            
        except Exception as e:
            self.logger.error(f"降级估算也失败: {e}, 使用最小值1")
            return 1

    def run_quick_test(self, test_agents: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        运行快速测试（使用简化配置）

        参数:
            test_agents: 测试智能体列表，如果为None则使用简化的智能体集合

        返回:
            测试结果字典
        """
        # 使用简化的智能体集合进行快速测试
        if test_agents is None:
            test_agents = ["NAA", "TAA", "FAA", "TRA"]  # 包含所有必需的智能体

        # 创建快速测试配置，使用当前配置的日期范围但限制为3天
        from datetime import datetime, timedelta

        # 获取当前配置的开始日期，如果没有则使用默认值
        config_start_date = self.config.get("start_date", "2023-01-01")
        try:
            start_dt = datetime.strptime(config_start_date, "%Y-%m-%d")
            end_dt = start_dt + timedelta(days=2)  # 快速测试只用3天
            quick_start_date = start_dt.strftime("%Y-%m-%d")
            quick_end_date = end_dt.strftime("%Y-%m-%d")
        except ValueError:
            # 如果日期格式有问题，使用默认值
            quick_start_date = "2023-01-01"
            quick_end_date = "2023-01-03"

        quick_config = {
            "start_date": quick_start_date,
            "end_date": quick_end_date,
            "stocks": ["AAPL"],
            "starting_cash": 100000,
            "simulation_days": 2,  # 限制模拟天数
            "fail_on_large_gaps": False,
            "verbose": False,
            "enable_concurrent_execution": True  # 启用并发执行进行测试
        }

        # 临时保存原配置
        original_config = self.config

        try:
            # 使用快速测试配置
            self.config = quick_config

            # 重新初始化交易模拟器
            self.trading_simulator = TradingSimulator(
                base_config=quick_config,
                logger=self.logger
            )

            # 运行评估（限制联盟数量）
            result = self.run(
                agents=None,  # 使用模拟智能体
                target_agents=test_agents,
                max_coalitions=3  # 只模拟前3个联盟
            )

            return result

        finally:
            # 恢复原配置
            self.config = original_config
            self.trading_simulator = TradingSimulator(
                base_config=original_config,
                logger=self.logger
            )
    
    def _log_periodic_shapley_summary(self, weekly_shapley_results: List[Dict[str, Any]], 
                                    target_agents: List[str]) -> None:
        """
        记录周期性Shapley值计算结果汇总
        
        参数:
            weekly_shapley_results: 周级Shapley值结果列表
            target_agents: 目标智能体列表
        """
        if not weekly_shapley_results:
            self.logger.warning("没有周期性Shapley值结果可以汇总")
            return
        
        self.logger.info("=" * 70)
        self.logger.info("周期性Shapley值计算结果汇总")
        self.logger.info("=" * 70)
        
        successful_weeks = [w for w in weekly_shapley_results if w.get("success", False)]
        failed_weeks = [w for w in weekly_shapley_results if not w.get("success", False)]
        
        self.logger.info(f"总周数: {len(weekly_shapley_results)}")
        self.logger.info(f"成功计算: {len(successful_weeks)} 周")
        self.logger.info(f"计算失败: {len(failed_weeks)} 周")
        
        if successful_weeks:
            self.logger.info("\n各智能体平均贡献度:")
            
            # 计算每个智能体的平均Shapley值
            agent_avg_contributions = {}
            for agent in target_agents:
                total_contribution = 0
                valid_weeks = 0
                
                for week_result in successful_weeks:
                    if agent in week_result.get("shapley_values", {}):
                        contribution = week_result["shapley_values"][agent]
                        if contribution is not None:
                            total_contribution += contribution
                            valid_weeks += 1
                
                if valid_weeks > 0:
                    agent_avg_contributions[agent] = total_contribution / valid_weeks
                else:
                    agent_avg_contributions[agent] = 0.0
                
                self.logger.info(f"  {agent}: {agent_avg_contributions[agent]:.6f}")
            
            # 识别表现最好和最差的智能体
            if agent_avg_contributions:
                best_agent = max(agent_avg_contributions.items(), key=lambda x: x[1])
                worst_agent = min(agent_avg_contributions.items(), key=lambda x: x[1])
                
                self.logger.info(f"\n表现最佳: {best_agent[0]} ({best_agent[1]:.6f})")
                self.logger.info(f"表现最差: {worst_agent[0]} ({worst_agent[1]:.6f})")
            
            # 分析贡献度趋势
            self._analyze_contribution_trends(successful_weeks, target_agents)
        
        if failed_weeks:
            self.logger.warning(f"\n计算失败的周次:")
            for week_result in failed_weeks:
                week_num = week_result.get("week", "未知")
                error = week_result.get("error", "未知错误")
                self.logger.warning(f"  第 {week_num} 周: {error}")
        
        self.logger.info("=" * 70)
    
    def _analyze_contribution_trends(self, successful_weeks: List[Dict[str, Any]], 
                                   target_agents: List[str]) -> None:
        """
        分析智能体贡献度趋势
        
        参数:
            successful_weeks: 成功计算的周级结果
            target_agents: 目标智能体列表
        """
        if len(successful_weeks) < 2:
            self.logger.info("周数不足，无法分析趋势")
            return
        
        self.logger.info("\n贡献度趋势分析:")
        
        for agent in target_agents:
            contributions = []
            weeks = []
            
            for week_result in successful_weeks:
                if agent in week_result.get("shapley_values", {}):
                    contribution = week_result["shapley_values"][agent]
                    if contribution is not None:
                        contributions.append(contribution)
                        weeks.append(week_result.get("week", len(contributions)))
            
            if len(contributions) >= 2:
                # 简单的趋势分析
                first_half_avg = sum(contributions[:len(contributions)//2]) / (len(contributions)//2)
                second_half_avg = sum(contributions[len(contributions)//2:]) / (len(contributions) - len(contributions)//2)
                
                trend = "上升" if second_half_avg > first_half_avg else "下降" if second_half_avg < first_half_avg else "稳定"
                trend_magnitude = abs(second_half_avg - first_half_avg)
                
                self.logger.info(f"  {agent}: {trend} (变化幅度: {trend_magnitude:.6f})")
            else:
                self.logger.info(f"  {agent}: 数据不足")
    
    def _save_periodic_shapley_results(self, weekly_shapley_results: List[Dict[str, Any]]) -> None:
        """
        保存周期性Shapley值结果到文件
        
        参数:
            weekly_shapley_results: 周级Shapley值结果列表
        """
        try:
            import json
            import os
            from datetime import datetime
            
            # 创建结果目录
            results_dir = "results/periodic_shapley"
            os.makedirs(results_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"periodic_shapley_{timestamp}.json"
            filepath = os.path.join(results_dir, filename)
            
            # 转换frozenset键为可序列化格式
            serializable_results = self._make_json_serializable(weekly_shapley_results)
            
            # 保存结果
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"周期性Shapley值结果已保存至: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存周期性Shapley值结果失败: {e}")
    
    def _make_json_serializable(self, obj):
        """
        将包含frozenset键的对象转换为JSON可序列化格式
        
        参数:
            obj: 要转换的对象
            
        返回:
            JSON可序列化的对象
        """
        if isinstance(obj, dict):
            serializable_dict = {}
            for key, value in obj.items():
                # 处理frozenset键
                if isinstance(key, frozenset):
                    # 将frozenset转换为排序后的元组的字符串表示
                    str_key = str(sorted(list(key)))
                else:
                    str_key = str(key)
                
                # 递归处理值
                serializable_dict[str_key] = self._make_json_serializable(value)
            return serializable_dict
        
        elif isinstance(obj, (list, tuple)):
            return [self._make_json_serializable(item) for item in obj]
        
        elif isinstance(obj, (set, frozenset)):
            return list(obj)
        
        elif hasattr(obj, '__dict__'):
            # 处理自定义对象
            return str(obj)
        
        else:
            return obj
    
    # ==================== OPRO优化相关方法 ====================
    
    def run_opro_optimization_cycle(self, 
                                  target_agents: Optional[List[str]] = None,
                                  force_optimization: bool = False) -> Dict[str, Any]:
        """
        运行OPRO优化循环
        
        参数:
            target_agents: 目标智能体列表，如果为None则使用所有默认智能体
            force_optimization: 是否强制优化，忽略时间间隔限制
            
        返回:
            优化结果字典
        """
        if not self.enable_opro:
            return {
                "success": False,
                "error": "OPRO优化未启用",
                "enabled": self.enable_opro
            }
        
        target_agents = target_agents or self.default_agents
        
        self.logger.info(f"开始OPRO优化循环: {len(target_agents)} 个智能体")
        
        try:
            # 获取当前活跃的提示词
            current_prompts = self._get_current_prompts(target_agents)
            
            # 运行批量优化
            optimization_result = self.opro_optimizer.optimize_all_agents(
                agent_ids=target_agents,
                current_prompts=current_prompts
            )
            
            # 处理优化结果
            if optimization_result.get("success", False):
                # 更新智能体提示词（如果使用了OPROBaseAgent）
                updated_agents = self._apply_optimized_prompts(optimization_result["results"])
                
                # 记录优化结果
                self._record_optimization_results(optimization_result)
                
                self.logger.info(f"OPRO优化循环完成: {optimization_result['successful_optimizations']}/{optimization_result['total_agents']} 智能体优化成功")
                
                return {
                    "success": True,
                    "optimization_result": optimization_result,
                    "updated_agents": updated_agents,
                    "next_evaluation_recommended": True
                }
            else:
                return optimization_result
                
        except Exception as e:
            self.logger.error(f"OPRO优化循环失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def run_with_opro_integration(self, 
                                agents: Optional[Dict[str, Any]] = None,
                                target_agents: Optional[List[str]] = None,
                                max_coalitions: Optional[int] = None,
                                run_optimization_before: bool = False,
                                run_optimization_after: bool = True) -> Dict[str, Any]:
        """
        运行完整的评估流程，集成OPRO优化
        
        参数:
            agents: 智能体实例字典
            target_agents: 目标智能体列表
            max_coalitions: 最大模拟联盟数量
            run_optimization_before: 评估前是否运行优化
            run_optimization_after: 评估后是否运行优化
            
        返回:
            包含评估结果和优化结果的完整字典
        """
        start_time = time.time()
        result = {
            "success": True,
            "opro_enabled": self.enable_opro,
            "optimization_results": {},
            "evaluation_result": {},
            "total_execution_time": 0.0
        }
        
        try:
            # 步骤1：评估前优化（可选）
            if run_optimization_before and self.enable_opro:
                self.logger.info("=" * 60)
                self.logger.info("步骤1: 评估前OPRO优化")
                self.logger.info("=" * 60)
                
                pre_optimization = self.run_opro_optimization_cycle(target_agents)
                result["optimization_results"]["pre_evaluation"] = pre_optimization
                
                if not pre_optimization.get("success", False):
                    self.logger.warning(f"评估前优化失败: {pre_optimization.get('error', '未知错误')}")
            
            # 步骤2：运行标准评估流程
            self.logger.info("=" * 60)
            self.logger.info("步骤2: 运行贡献度评估")
            self.logger.info("=" * 60)
            
            evaluation_result = self.run(
                agents=agents,
                target_agents=target_agents,
                max_coalitions=max_coalitions
            )
            
            result["evaluation_result"] = evaluation_result
            
            if not evaluation_result.get("success", False):
                result["success"] = False
                result["error"] = f"评估失败: {evaluation_result.get('error', '未知错误')}"
                return result
            
            # 步骤3：更新历史得分
            if self.enable_opro:
                self._update_historical_scores_from_evaluation(evaluation_result)
            
            # 步骤4：评估后优化（可选）
            if run_optimization_after and self.enable_opro:
                self.logger.info("=" * 60)
                self.logger.info("步骤3: 评估后OPRO优化")
                self.logger.info("=" * 60)
                
                post_optimization = self.run_opro_optimization_cycle(target_agents)
                result["optimization_results"]["post_evaluation"] = post_optimization
                
                if not post_optimization.get("success", False):
                    self.logger.warning(f"评估后优化失败: {post_optimization.get('error', '未知错误')}")
            
            # 计算总执行时间
            result["total_execution_time"] = time.time() - start_time
            
            self.logger.info("=" * 60)
            self.logger.info(f"OPRO集成评估完成，总耗时: {result['total_execution_time']:.2f}s")
            self.logger.info("=" * 60)
            
            return result
            
        except Exception as e:
            result["success"] = False
            result["error"] = str(e)
            result["total_execution_time"] = time.time() - start_time
            self.logger.error(f"OPRO集成评估失败: {e}")
            return result
    
    def _get_current_prompts(self, agent_ids: List[str]) -> Dict[str, str]:
        """获取当前智能体的提示词"""
        current_prompts = {}
        
        for agent_id in agent_ids:
            if agent_id in self.agents:
                agent = self.agents[agent_id]
                if hasattr(agent, 'get_prompt_template'):
                    current_prompts[agent_id] = agent.get_prompt_template()
                else:
                    current_prompts[agent_id] = f"默认提示词 - {agent_id}"
            else:
                # 如果没有智能体实例，使用默认提示词
                current_prompts[agent_id] = self._get_default_prompt_for_agent(agent_id)
        
        return current_prompts
    
    def _get_default_prompt_for_agent(self, agent_id: str) -> str:
        """获取智能体的默认提示词"""
        default_prompts = {
            "NAA": "你是一个专业的新闻分析师，分析市场新闻对股票的影响。",
            "TAA": "你是一个专业的技术分析师，通过图表和技术指标分析股票趋势。",
            "FAA": "你是一个专业的基本面分析师，分析公司财务状况和内在价值。",
            "BOA": "你是一个看涨分析师，构建乐观的市场展望。",
            "BeOA": "你是一个看跌分析师，识别市场风险和负面因素。",
            "NOA": "你是一个中性观察者，提供平衡的市场分析。",
            "TRA": "你是一个专业的积极交易员，负责做出最终的交易决策。你的目标是通过主动交易获得收益，避免过度保守的持有策略。当分析师给出明确信号时，要积极执行买卖操作。"
        }
        
        return default_prompts.get(agent_id, f"默认智能体提示词 - {agent_id}")
    
    def _apply_optimized_prompts(self, optimization_results: Dict[str, Any]) -> List[str]:
        """应用优化后的提示词到智能体实例"""
        updated_agents = []
        
        for agent_id, result in optimization_results.items():
            if not result.get("success", False):
                continue
            
            optimized_prompt = result.get("optimized_prompt")
            if not optimized_prompt:
                continue
            
            # 如果有智能体实例且支持动态提示词更新
            if agent_id in self.agents:
                agent = self.agents[agent_id]
                if hasattr(agent, 'update_prompt'):
                    try:
                        success = agent.update_prompt(
                            new_prompt=optimized_prompt,
                            source="opro",
                            metadata={
                                "estimated_improvement": result.get("improvement", 0),
                                "estimated_score": result.get("estimated_score", 0)
                            }
                        )
                        
                        if success:
                            updated_agents.append(agent_id)
                            self.logger.info(f"智能体 {agent_id} 提示词更新成功")
                        else:
                            self.logger.warning(f"智能体 {agent_id} 提示词更新失败")
                            
                    except Exception as e:
                        self.logger.error(f"更新智能体 {agent_id} 提示词时出错: {e}")
        
        return updated_agents
    
    def _record_optimization_results(self, optimization_result: Dict[str, Any]):
        """记录优化结果到历史得分管理器"""
        if not self.historical_score_manager:
            return
        
        try:
            for agent_id, result in optimization_result.get("results", {}).items():
                if result.get("success", False):
                    prompt = result.get("optimized_prompt", "")
                    estimated_score = result.get("estimated_score", 0.0)
                    
                    metadata = {
                        "optimization_type": "opro",
                        "estimated_improvement": result.get("improvement", 0),
                        "candidates_generated": result.get("candidates_generated", 0),
                        "optimization_time": result.get("optimization_time", 0)
                    }
                    
                    self.historical_score_manager.store_optimization_result(
                        agent_id=agent_id,
                        prompt=prompt,
                        estimated_score=estimated_score,
                        metadata=metadata
                    )
                    
        except Exception as e:
            self.logger.error(f"记录优化结果失败: {e}")
    
    def _update_historical_scores_from_evaluation(self, evaluation_result: Dict[str, Any]):
        """从评估结果更新历史得分"""
        if not self.historical_score_manager:
            return
        
        try:
            # 从评估结果中提取Shapley值
            shapley_values = evaluation_result.get("shapley_values", {})
            
            if shapley_values:
                evaluation_date = datetime.now().isoformat()
                
                # 获取当前活跃的提示词哈希
                active_prompts = self.historical_score_manager.get_active_prompts()
                
                for agent_id, score in shapley_values.items():
                    if agent_id in active_prompts:
                        # 计算提示词哈希
                        prompt = active_prompts[agent_id]
                        import hashlib
                        prompt_hash = hashlib.md5(prompt.encode('utf-8')).hexdigest()
                        
                        # 更新实际得分
                        self.historical_score_manager.update_actual_score(
                            agent_id=agent_id,
                            prompt_hash=prompt_hash,
                            actual_score=float(score),
                            evaluation_date=evaluation_date
                        )
            
            # 同时处理周期性结果
            periodic_results = evaluation_result.get("periodic_shapley_results", {})
            if periodic_results and periodic_results.get("weekly_results"):
                for week_result in periodic_results["weekly_results"]:
                    if week_result.get("success", False):
                        week_shapley_values = week_result.get("shapley_values", {})
                        for agent_id, score in week_shapley_values.items():
                            # 这些数据已经在HistoricalScoreManager初始化时自动加载了
                            pass
                            
        except Exception as e:
            self.logger.error(f"从评估结果更新历史得分失败: {e}")
    
    def get_opro_dashboard_data(self) -> Dict[str, Any]:
        """获取OPRO仪表板数据"""
        if not self.enable_opro:
            return {
                "opro_enabled": False,
                "error": "OPRO功能未启用"
            }
        
        try:
            dashboard_data = {
                "opro_enabled": True,
                "system_stats": {},
                "agent_performance": {},
                "optimization_history": {},
                "recommendations": []
            }
            
            # 获取系统统计
            if self.opro_optimizer:
                dashboard_data["system_stats"]["optimizer"] = self.opro_optimizer.get_stats()
            
            if self.historical_score_manager:
                dashboard_data["system_stats"]["score_manager"] = self.historical_score_manager.get_summary_stats()
                
                # 获取智能体性能比较
                dashboard_data["agent_performance"] = self.historical_score_manager.get_cross_agent_comparison()
                
                # 获取优化效果
                dashboard_data["optimization_history"] = self.historical_score_manager.get_optimization_effectiveness()
            
            # 生成优化建议
            dashboard_data["recommendations"] = self._generate_optimization_recommendations()
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"获取OPRO仪表板数据失败: {e}")
            return {
                "opro_enabled": True,
                "error": str(e)
            }
    
    def _generate_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        try:
            if not self.historical_score_manager:
                return recommendations
            
            # 分析每个智能体的趋势
            for agent_id in self.default_agents:
                trends = self.historical_score_manager.get_score_trends(agent_id)
                
                if trends["trend"] == "declining":
                    recommendations.append({
                        "type": "optimization_needed",
                        "agent_id": agent_id,
                        "priority": "high",
                        "message": f"{agent_id} 性能呈下降趋势，建议立即优化",
                        "details": {
                            "trend": trends["trend"],
                            "recent_average": trends["recent_average"],
                            "historical_average": trends["historical_average"]
                        }
                    })
                elif trends["volatility"] > 0.5:
                    recommendations.append({
                        "type": "stability_issue",
                        "agent_id": agent_id,
                        "priority": "medium",
                        "message": f"{agent_id} 性能波动较大，建议进行稳定性优化",
                        "details": {
                            "volatility": trends["volatility"],
                            "data_points": trends["data_points"]
                        }
                    })
            
            # 检查优化频率
            optimizer_stats = self.opro_optimizer.get_stats() if self.opro_optimizer else {}
            if optimizer_stats.get("total_optimizations", 0) == 0:
                recommendations.append({
                    "type": "first_optimization",
                    "priority": "high",
                    "message": "尚未进行任何优化，建议运行首次OPRO优化循环",
                    "details": {}
                })
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            return []
    
    def export_opro_data(self, output_dir: str = "results/opro_export") -> Dict[str, Any]:
        """导出OPRO相关数据"""
        if not self.enable_opro:
            return {
                "success": False,
                "error": "OPRO功能未启用"
            }
        
        try:
            import os
            from datetime import datetime
            
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            exported_files = []
            
            # 导出历史得分数据
            if self.historical_score_manager:
                score_file = os.path.join(output_dir, f"historical_scores_{timestamp}.json")
                if self.historical_score_manager.export_data(score_file):
                    exported_files.append(score_file)
            
            # 导出优化器统计
            if self.opro_optimizer:
                stats_file = os.path.join(output_dir, f"optimizer_stats_{timestamp}.json")
                with open(stats_file, 'w', encoding='utf-8') as f:
                    json.dump(self.opro_optimizer.get_stats(), f, indent=2, ensure_ascii=False)
                exported_files.append(stats_file)
            
            # 导出仪表板数据
            dashboard_file = os.path.join(output_dir, f"dashboard_data_{timestamp}.json")
            dashboard_data = self.get_opro_dashboard_data()
            with open(dashboard_file, 'w', encoding='utf-8') as f:
                json.dump(dashboard_data, f, indent=2, ensure_ascii=False)
            exported_files.append(dashboard_file)
            
            self.logger.info(f"OPRO数据导出成功: {len(exported_files)} 个文件")
            
            return {
                "success": True,
                "exported_files": exported_files,
                "export_directory": output_dir
            }
            
        except Exception as e:
            self.logger.error(f"OPRO数据导出失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_default_llm_agents(self) -> Dict[str, Any]:
        """
        创建默认的LLM智能体
        
        返回:
            智能体实例字典
        """
        if not self.llm_interface:
            self.logger.warning("LLM接口不可用，无法创建LLM智能体")
            return {}
        
        try:
            from agents.analyst_agents import NewsAnalystAgent, TechnicalAnalystAgent, FundamentalAnalystAgent
            from agents.outlook_agents import BullishOutlookAgent, BearishOutlookAgent, NeutralObserverAgent
            from agents.trader_agent import TraderAgent
            
            agents = {}
            
            # 创建分析层智能体
            agents["NAA"] = NewsAnalystAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )

            agents["TAA"] = TechnicalAnalystAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )

            agents["FAA"] = FundamentalAnalystAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )

            # 创建观点层智能体
            agents["BOA"] = BullishOutlookAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )

            agents["BeOA"] = BearishOutlookAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )
            
            agents["NOA"] = NeutralObserverAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )

            # 创建交易员智能体
            agents["TRA"] = TraderAgent(
                llm_interface=self.llm_interface,
                logger=self.logger,
                interaction_logger=self.interaction_logger
            )
            
            self.logger.info(f"[SUCCESS] 成功创建默认LLM智能体: {list(agents.keys())}")
            return agents
            
        except Exception as e:
            self.logger.error(f"创建默认LLM智能体失败: {e}")
            return {}
