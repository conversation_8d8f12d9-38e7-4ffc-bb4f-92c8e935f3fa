#!/usr/bin/env python3
"""
联盟存储管理器测试脚本

测试新的联盟特定存储结构，验证：
1. 联盟文件夹创建
2. 实验数据存储
3. Shapley结果保存
4. 数据检索功能
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from contribution_assessment.coalition_storage_manager import CoalitionStorageManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_coalition_storage.log')
        ]
    )
    return logging.getLogger(__name__)

def test_basic_storage_operations(logger):
    """测试基本存储操作"""
    logger.info("=" * 50)
    logger.info("测试基本存储操作")
    
    # 创建存储管理器
    storage_manager = CoalitionStorageManager(logger=logger)
    
    # 开始新实验
    experiment_config = {
        "test_type": "basic_operations",
        "target_agents": ["NAA", "TAA", "TRA"],
        "description": "测试基本存储功能"
    }
    
    experiment_id = storage_manager.start_new_experiment(experiment_config)
    logger.info(f"创建实验: {experiment_id}")
    
    # 测试联盟存储创建
    test_coalitions = [
        {"NAA", "TRA"},
        {"TAA", "TRA"}, 
        {"NAA", "TAA", "TRA"}
    ]
    
    for coalition in test_coalitions:
        coalition_info = {
            "coalition_type": "test",
            "expected_performance": "unknown"
        }
        
        coalition_dir = storage_manager.create_coalition_storage(coalition, coalition_info)
        logger.info(f"创建联盟存储: {coalition} -> {coalition_dir}")
        
        # 模拟交易结果
        simulation_result = {
            "sharpe_ratio": 0.5 + len(coalition) * 0.1,
            "daily_returns": [0.01, -0.005, 0.02, 0.015, -0.01] * 10,
            "total_days": 50,
            "simulation_time": 120.5,
            "coalition_members": sorted(list(coalition))
        }
        
        # 保存模拟结果
        storage_manager.save_coalition_simulation_result(
            coalition=coalition,
            simulation_result=simulation_result
        )
        logger.info(f"保存联盟模拟结果: {coalition}")
    
    # 测试Shapley结果保存
    shapley_values = {
        "NAA": 0.15,
        "TAA": 0.12,
        "TRA": 0.25
    }
    
    coalition_values = {
        frozenset(["NAA", "TRA"]): 0.35,
        frozenset(["TAA", "TRA"]): 0.32,
        frozenset(["NAA", "TAA", "TRA"]): 0.52
    }
    
    calculation_metadata = {
        "calculation_method": "standard_shapley",
        "total_coalitions": len(coalition_values),
        "test_run": True
    }
    
    storage_manager.save_shapley_calculation_result(
        shapley_values=shapley_values,
        coalition_values=coalition_values,
        calculation_metadata=calculation_metadata
    )
    logger.info("保存Shapley计算结果")
    
    # 完成实验
    experiment_summary = {
        "total_coalitions": len(test_coalitions),
        "total_agents": len(shapley_values),
        "test_status": "completed"
    }
    
    storage_manager.finish_experiment(experiment_summary)
    logger.info("完成实验")
    
    return experiment_id

def test_data_retrieval(storage_manager, experiment_id, logger):
    """测试数据检索功能"""
    logger.info("=" * 50)
    logger.info("测试数据检索功能")
    
    # 测试联盟数据检索
    test_coalition = {"NAA", "TRA"}
    coalition_data = storage_manager.get_coalition_data(
        coalition=test_coalition,
        experiment_id=experiment_id
    )
    
    if coalition_data:
        logger.info(f"成功检索联盟数据: {test_coalition}")
        logger.info(f"  - 联盟信息: {coalition_data.get('info', {}).get('coalition_name', 'N/A')}")
        logger.info(f"  - 夏普比率: {coalition_data.get('performance', {}).get('sharpe_ratio', 'N/A')}")
    else:
        logger.error(f"检索联盟数据失败: {test_coalition}")
    
    # 测试实验总结检索
    experiment_summary = storage_manager.get_experiment_summary(experiment_id)
    if experiment_summary:
        logger.info("成功检索实验总结")
        logger.info(f"  - 实验状态: {experiment_summary.get('experiment_info', {}).get('status', 'N/A')}")
        logger.info(f"  - Shapley值数量: {len(experiment_summary.get('shapley_results', {}).get('shapley_values', {}))}")
    else:
        logger.error("检索实验总结失败")

def test_experiment_listing(storage_manager, logger):
    """测试实验列表功能"""
    logger.info("=" * 50)
    logger.info("测试实验列表功能")
    
    experiments = storage_manager.list_experiments(limit=5)
    logger.info(f"找到 {len(experiments)} 个实验:")
    
    for exp in experiments:
        logger.info(f"  - {exp['experiment_id']}: {exp['status']} ({exp['coalition_count']} 个联盟)")

def verify_directory_structure(logger):
    """验证目录结构"""
    logger.info("=" * 50)
    logger.info("验证目录结构")
    
    data_dir = Path("data")
    shapley_dir = data_dir / "shapley_experiments"
    
    if shapley_dir.exists():
        logger.info(f"Shapley实验目录存在: {shapley_dir}")
        
        # 列出实验目录
        experiment_dirs = [d for d in shapley_dir.iterdir() if d.is_dir() and d.name.startswith("experiment_")]
        logger.info(f"找到 {len(experiment_dirs)} 个实验目录")
        
        # 检查最新实验的联盟目录
        if experiment_dirs:
            latest_exp = max(experiment_dirs, key=lambda x: x.name)
            logger.info(f"最新实验: {latest_exp.name}")
            
            coalition_dirs = [d for d in latest_exp.iterdir() if d.is_dir() and d.name.startswith("coalition_")]
            logger.info(f"  - 联盟目录数量: {len(coalition_dirs)}")
            
            for coalition_dir in coalition_dirs:
                logger.info(f"  - 联盟: {coalition_dir.name}")
                
                # 检查联盟文件
                files = list(coalition_dir.glob("*.json"))
                logger.info(f"    - JSON文件: {[f.name for f in files]}")
                
                # 检查代理日志目录
                agent_logs_dir = coalition_dir / "agent_logs"
                if agent_logs_dir.exists():
                    agent_dirs = [d.name for d in agent_logs_dir.iterdir() if d.is_dir()]
                    logger.info(f"    - 代理日志目录: {agent_dirs}")
    else:
        logger.warning(f"Shapley实验目录不存在: {shapley_dir}")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始联盟存储管理器测试")
    
    try:
        # 测试基本存储操作
        experiment_id = test_basic_storage_operations(logger)
        
        # 创建新的存储管理器实例进行检索测试
        storage_manager = CoalitionStorageManager(logger=logger)
        
        # 测试数据检索
        test_data_retrieval(storage_manager, experiment_id, logger)
        
        # 测试实验列表
        test_experiment_listing(storage_manager, logger)
        
        # 验证目录结构
        verify_directory_structure(logger)
        
        logger.info("=" * 50)
        logger.info("所有测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
