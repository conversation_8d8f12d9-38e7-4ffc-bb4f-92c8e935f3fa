2025-07-05 21:54:57,525 - __main__ - INFO - ====================================================================================================
2025-07-05 21:54:57,525 - __main__ - INFO - OPRO系统启动
2025-07-05 21:54:57,525 - __main__ - INFO - ====================================================================================================
2025-07-05 21:54:57,525 - __main__ - INFO - 运行模式: integrated
2025-07-05 21:54:57,525 - __main__ - INFO - LLM提供商: zhipuai
2025-07-05 21:54:57,525 - __main__ - INFO - OPRO启用: True
2025-07-05 21:54:57,525 - __main__ - INFO - 数据存储启用: True
2025-07-05 21:54:57,525 - __main__ - INFO - 代理日志记录启用: True
2025-07-05 21:54:57,527 - __main__ - INFO - 初始化系统...
2025-07-05 21:54:57,527 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:54:57,527 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:57,529 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:57,649 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:57,650 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:57,764 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:57,765 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:57,902 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:54:57,902 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:54:57,902 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-05 21:54:57,903 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-05
2025-07-05 21:54:57,903 - __main__ - INFO - 设置实验日期: 2025-07-05
2025-07-05 21:54:57,904 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-05)
2025-07-05 21:54:57,904 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:54:57,904 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:54:57,905 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:54:57,905 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:54:57,905 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:54:57,905 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:54:57,906 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:54:57,907 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:54:57,907 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:54:57,907 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-05 21:54:57,907 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-05 21:54:57,909 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:54:57,909 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:54:57,910 - __main__ - INFO - 可视化管理器初始化完成
2025-07-05 21:54:57,917 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-05 21:54:57,918 - __main__ - INFO - A/B测试框架初始化完成
2025-07-05 21:54:57,918 - __main__ - INFO - 数据分析工具初始化完成
2025-07-05 21:54:57,918 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-05 21:54:57,919 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-05 21:54:57,919 - __main__ - INFO - 备份管理器初始化完成
2025-07-05 21:54:57,919 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-05 21:54:57,919 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-05 21:54:57,919 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-05 21:54:57,919 - __main__ - INFO - 分析缓存初始化完成
2025-07-05 21:54:57,919 - __main__ - INFO - 联盟管理器初始化完成
2025-07-05 21:54:57,919 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:54:57,920 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:54:57,920 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-05 21:54:57,920 - __main__ - DEBUG - 确保目录存在: data
2025-07-05 21:54:57,920 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-05 21:54:57,920 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-05 21:54:57,920 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-05 21:54:57,920 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-05 21:54:57,921 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-05 21:54:57,921 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:54:57,922 - __main__ - INFO - 自动备份线程已启动
2025-07-05 21:54:57,923 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-05 21:54:57,924 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-05 21:54:57,924 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-05 21:54:57,924 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-05 21:54:57,924 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-05 21:54:57,925 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:57,925 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:58,049 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:58,049 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:58,059 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250705_215457\\trading\\2025-01-01\\BeOA\\inputs.json'
2025-07-05 21:54:58,060 - __main__ - INFO - 数据备份完成: backup_20250705_215457 (0.04 MB)
2025-07-05 21:54:58,174 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-05 21:54:58,176 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-05 21:54:58,295 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-05 21:54:58,296 - __main__ - INFO - 数据库初始化完成
2025-07-05 21:54:58,296 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-05 21:54:58,385 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-05 21:54:58,385 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-05 21:54:58,385 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-05 21:54:58,385 - __main__ - INFO - OPRO优化器初始化完成
2025-07-05 21:54:58,385 - __main__ - INFO - OPRO组件初始化成功
2025-07-05 21:54:58,385 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-05 21:54:58,385 - __main__ - INFO - 系统初始化完成
2025-07-05 21:54:58,386 - __main__ - INFO - ================================================================================
2025-07-05 21:54:58,386 - __main__ - INFO - 运行模式: 集成模式（评估+优化）
2025-07-05 21:54:58,386 - __main__ - INFO - ================================================================================
2025-07-05 21:54:58,386 - __main__ - INFO - 开始运行完整日期范围的交易系统: 2025-01-01 到 2025-01-01
2025-07-05 21:54:58,386 - __main__ - INFO - ====================================================================================================
2025-07-05 21:54:58,386 - __main__ - INFO - 🚀 启动完整日期范围交易系统
2025-07-05 21:54:58,386 - __main__ - INFO - ====================================================================================================
2025-07-05 21:54:58,386 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-01-01
2025-07-05 21:54:58,386 - __main__ - INFO - 🤖 目标智能体: 所有默认智能体
2025-07-05 21:54:58,387 - __main__ - INFO - 🔄 OPRO优化: 启用
2025-07-05 21:54:58,387 - __main__ - INFO - 步骤1: 生成交易日期列表...
2025-07-05 21:54:58,387 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-01-01
2025-07-05 21:54:58,387 - __main__ - INFO - 📊 总交易日数: 1
2025-07-05 21:54:58,387 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-05 21:54:58,388 - __main__ - INFO - 🗓️  最后交易日: 2025-01-01
2025-07-05 21:54:58,388 - __main__ - INFO - ✅ 生成了 1 个交易日
2025-07-05 21:54:58,388 - __main__ - INFO - 步骤3: 开始每日交易决策循环...
2025-07-05 21:54:58,388 - __main__ - INFO - 🔄 开始每日交易循环，共 1 个交易日
2025-07-05 21:54:58,388 - __main__ - INFO - 📅 处理第 1/1 天: 2025-01-01
2025-07-05 21:54:58,389 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:54:58,389 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 NAA 初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 TAA 初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 FAA 初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 BOA 初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 BeOA 初始化完成
2025-07-05 21:54:58,396 - __main__ - INFO - 智能体 NOA 初始化完成
2025-07-05 21:54:58,397 - __main__ - INFO - 智能体 TRA 初始化完成
2025-07-05 21:54:58,397 - __main__ - INFO - [SUCCESS] 成功创建默认LLM智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:54:58,397 - __main__ - INFO - 开始贡献度评估流程
2025-07-05 21:54:58,397 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:54:58,397 - __main__ - INFO - 可用智能体实例: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-05 21:54:58,397 - __main__ - INFO - 开始新实验: experiment_20250705_215458
2025-07-05 21:54:58,399 - __main__ - INFO - 开始新实验: experiment_20250705_215458
2025-07-05 21:54:58,399 - __main__ - INFO - ==================================================
2025-07-05 21:54:58,399 - __main__ - INFO - 阶段1: 分析缓存
2025-07-05 21:54:58,399 - __main__ - INFO - 使用智能体实例进行分析...
2025-07-05 21:54:58,399 - __main__ - INFO - 开始分析缓存阶段...
2025-07-05 21:54:58,427 - __main__ - INFO - ✅ 使用真实环境状态，包含 1 个日期的新闻数据
2025-07-05 21:54:58,427 - __main__ - INFO - 开始填充分析缓存，共 7 个智能体
2025-07-05 21:54:58,429 - __main__ - INFO - 分析缓存已清空
2025-07-05 21:54:58,429 - __main__ - INFO - 执行分析智能体: NAA
2025-07-05 21:54:58,430 - __main__ - DEBUG - 记录代理输入: NAA - input_20250705_215458_a935e6d8
2025-07-05 21:54:58,430 - __main__ - DEBUG - 记录代理提示词: NAA - prompt_20250705_215458_341cd689
2025-07-05 21:54:58,431 - __main__ - INFO - ================================================================================
2025-07-05 21:54:58,431 - __main__ - INFO - 🤖 NAA LLM输入:
2025-07-05 21:54:58,431 - __main__ - INFO - ----------------------------------------
2025-07-05 21:54:58,431 - __main__ - INFO - 你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供明确的交易建议信号

**评分标准**：
- sentiment > 0.3: 建议买入信号
- sentiment < -0.3: 建议卖出信号
- -0.3 <= sentiment <= 0.3: 中性信号

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）

**重要**：请给出明确的交易信号，避免过度中性的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:54:58,431 - __main__ - INFO - ----------------------------------------
2025-07-05 21:54:58,431 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:54:58,432 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:54:58,432 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:54:58,432 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-05 21:54:58,434 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002384666E720>
2025-07-05 21:54:58,435 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:54:58,435 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:54:58,436 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-05 21:54:58,436 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:54:58,436 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-05 21:54:58,436 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-05 21:54:58,436 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000023846AE5A50> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-05 21:54:58,462 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000023846943C20>
2025-07-05 21:54:58,463 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:54:58,463 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:54:58,463 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:54:58,463 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:54:58,463 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:03,108 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:55:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117517236987343488e0052786e2204a6c9964eadff60b144fcedf5;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215458b5e24bb332ba4fb8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:03,110 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:03,110 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:03,111 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:03,112 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:03,112 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:03,113 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:03,114 - __main__ - INFO - 🤖 NAA LLM输出:
2025-07-05 21:55:03,115 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:03,115 - __main__ - INFO - {'sentiment': -0.05, 'trading_signal': 'neutral', 'signal_strength': 0.1, 'summary': '今日市场新闻以中性预测为主，没有显著影响AAPL的负面或正面报道。', 'key_events': [{'event': '10 Stock Market Predictions for 2025', 'sentiment': 'Neutral', 'impact': 'Minimal'}], 'impact_assessment': 'No significant news affecting AAPL today. Predictions are neutral.', 'confidence': 0.9}
2025-07-05 21:55:03,115 - __main__ - INFO - ================================================================================
2025-07-05 21:55:03,117 - __main__ - DEBUG - 记录代理输出: NAA - output_20250705_215503_b7905e4e
2025-07-05 21:55:03,117 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-05 21:55:03,117 - __main__ - INFO - 智能体 NAA 执行成功 (4.69s)
2025-07-05 21:55:03,117 - __main__ - INFO - 执行分析智能体: TAA
2025-07-05 21:55:03,119 - __main__ - DEBUG - 记录代理输入: TAA - input_20250705_215503_abe574fe
2025-07-05 21:55:03,119 - __main__ - DEBUG - 记录代理提示词: TAA - prompt_20250705_215503_a17abb67
2025-07-05 21:55:03,119 - __main__ - INFO - ================================================================================
2025-07-05 21:55:03,120 - __main__ - INFO - 🤖 TAA LLM输入:
2025-07-05 21:55:03,120 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:03,120 - __main__ - INFO - 你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 提供明确的技术面交易建议

**评分标准**：
- technical_score > 0.3: 技术面看涨，建议买入
- technical_score < -0.3: 技术面看跌，建议卖出
- -0.3 <= technical_score <= 0.3: 技术面中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- technical_score: 技术评分（-1到1）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）

**重要**：基于技术指标给出明确的买卖建议，避免模糊的中性判断。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:03,120 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:03,121 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:03,121 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:03,121 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:03,122 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:03,122 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:03,123 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:03,123 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:03,123 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:19,103 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:55:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215503dcfbb9b31ef145f3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:19,104 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:19,105 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:19,106 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:19,106 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:19,107 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:19,107 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:19,109 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Extra data: line 28 column 1 (char 745)
2025-07-05 21:55:19,110 - __main__ - INFO - 🤖 TAA LLM输出:
2025-07-05 21:55:19,110 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:19,111 - __main__ - INFO - {'content': '```json\n{\n  "trend": "neutral",\n  "technical_score": 0.05,\n  "trading_signal": "neutral",\n  "signal_strength": 0.05,\n  "support_level": 150.00,\n  "resistance_level": 160.00,\n  "indicators": {\n    "RSI": {\n      "current_value": 50.2,\n      "interpretation": "Indicates a neutral market condition."\n    },\n    "MACD": {\n      "signal_line": 0.1,\n      "histogram": 0.05,\n      "interpretation": "MACD signal line is slightly above the zero line, suggesting a weak bullish trend."\n    },\n    "Moving_Averages": {\n      "50_day_MA": 155.00,\n      "200_day_MA": 165.00,\n      "interpretation": "The stock is currently below its 50-day and 200-day moving averages, suggesting a bearish trend in the short and medium term."\n    }\n  },\n  "confidence": 0.8\n}\n```\n\n**解释**：\n- 趋势方向：中性，因为技术指标显示没有明显的上涨或下跌趋势。\n- 技术评分：0.05，略偏向看涨，但信号非常弱。\n- 交易信号：中性，因为信号太弱，不足以形成明确的买卖建议。\n- 信号强度：0.05，信号非常弱。\n- 支撑位：150.00，基于历史价格和当前趋势。\n- 阻力位：160.00，基于历史价格和当前趋势。\n- 技术指标分析：RSI接近50，表明市场中性；MACD略高于零线，表明轻微看涨趋势；移动平均线显示股票低于50日和200日均线，表明短期和中期趋势可能看跌。\n- 分析信心度：0.8，基于当前的技术指标和价格走势。', 'type': 'text_response'}
2025-07-05 21:55:19,112 - __main__ - INFO - ================================================================================
2025-07-05 21:55:19,113 - __main__ - DEBUG - 记录代理输出: TAA - output_20250705_215519_5b7cacc7
2025-07-05 21:55:19,113 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-05 21:55:19,113 - __main__ - INFO - 智能体 TAA 执行成功 (16.00s)
2025-07-05 21:55:19,115 - __main__ - INFO - 执行分析智能体: FAA
2025-07-05 21:55:19,116 - __main__ - DEBUG - 记录代理输入: FAA - input_20250705_215519_1499a4c8
2025-07-05 21:55:19,117 - __main__ - DEBUG - 记录代理提示词: FAA - prompt_20250705_215519_a71e5d81
2025-07-05 21:55:19,117 - __main__ - INFO - ================================================================================
2025-07-05 21:55:19,117 - __main__ - INFO - 🤖 FAA LLM输入:
2025-07-05 21:55:19,118 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:19,118 - __main__ - INFO - 你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 提供基于价值的交易建议

**评分标准**：
- valuation = "undervalued": 建议买入
- valuation = "overvalued": 建议卖出
- valuation = "fair": 中性，但倾向于根据其他因素决定

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- valuation_score: 估值评分（-1到1，-1严重高估，1严重低估）
- trading_signal: 交易信号（buy/sell/neutral）
- signal_strength: 信号强度（0到1）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）

**重要**：基于估值分析给出明确的投资建议，避免过度保守的评估。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:19,118 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:19,118 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:19,118 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:19,118 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:19,119 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:19,119 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:19,119 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:19,119 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:19,119 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:23,323 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:55:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215519d7443b9154354cc0'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:23,324 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:23,325 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:23,325 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:23,326 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:23,326 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:23,326 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:23,328 - __main__ - INFO - 🤖 FAA LLM输出:
2025-07-05 21:55:23,328 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:23,329 - __main__ - INFO - {'valuation': 'undervalued', 'valuation_score': 0.9, 'trading_signal': 'buy', 'signal_strength': 0.9, 'financial_health': 8, 'competitive_position': 'strong', 'long_term_outlook': 'positive', 'intrinsic_value_estimate': 150.0, 'confidence': 0.95}
2025-07-05 21:55:23,329 - __main__ - INFO - ================================================================================
2025-07-05 21:55:23,331 - __main__ - DEBUG - 记录代理输出: FAA - output_20250705_215523_86d05f93
2025-07-05 21:55:23,332 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-05 21:55:23,332 - __main__ - INFO - 智能体 FAA 执行成功 (4.22s)
2025-07-05 21:55:23,332 - __main__ - INFO - 执行分析智能体: BOA
2025-07-05 21:55:23,334 - __main__ - DEBUG - 记录代理输入: BOA - input_20250705_215523_9191be7e
2025-07-05 21:55:23,334 - __main__ - DEBUG - 记录代理提示词: BOA - prompt_20250705_215523_56a0a215
2025-07-05 21:55:23,335 - __main__ - INFO - ================================================================================
2025-07-05 21:55:23,335 - __main__ - INFO - 🤖 BOA LLM输入:
2025-07-05 21:55:23,335 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:23,335 - __main__ - INFO - 你是一个看涨倾向的市场分析师，专门构建乐观的市场展望。

你的任务是：
1. 基于前序分析师的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 提供明确的买入建议

**重要指导**：
- 积极寻找买入机会和看涨信号
- 即使在不确定情况下，也要倾向于乐观解读
- 给出明确的买入建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bullish）
- trading_signal: 交易信号（通常为buy）
- signal_strength: 信号强度（0到1）
- bullish_factors: 看涨因素列表
- target_price: 目标价位
- upside_potential: 上涨潜力（百分比）
- time_horizon: 时间框架
- risk_factors: 风险因素
- confidence: 分析信心度（0到1）

**记住**：作为看涨分析师，你的职责是找到买入理由。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:23,336 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:23,336 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:23,336 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:23,336 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:23,337 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:23,337 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:23,337 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:23,337 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:23,337 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:35,190 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:55:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052155233d80b9c692a94e02'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:35,191 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:35,192 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:35,193 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:35,193 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:35,193 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:35,194 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:35,196 - __main__ - INFO - 🤖 BOA LLM输出:
2025-07-05 21:55:35,196 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:35,197 - __main__ - INFO - {'outlook': 'bullish', 'trading_signal': 'buy', 'signal_strength': 0.95, 'bullish_factors': [{'factor': 'Positive Analyst Predictions', 'description': 'Multiple analysts have released positive predictions for 2025, indicating strong growth potential.'}, {'factor': 'Economic Recovery', 'description': 'Global economic indicators suggest a strong recovery, which is expected to drive consumer spending and corporate profits.'}, {'factor': 'Product Launch', 'description': 'AAPL is rumored to be launching a new product line that is expected to be highly successful and drive sales.'}, {'factor': 'Strong Earnings Reports', 'description': "AAPL's recent earnings report showed strong revenue growth and improved profitability, which has bolstered investor confidence."}, {'factor': 'Low Interest Rates', 'description': 'The current low interest rate environment is making equities more attractive to investors, potentially driving up stock prices.'}], 'target_price': 200.0, 'upside_potential': 20.0, 'time_horizon': '1 year', 'risk_factors': [{'factor': 'Political Instability', 'description': 'Political tensions in key markets could impact global economic stability and potentially affect stock prices.'}, {'factor': 'Economic Slowdown', 'description': 'A sudden economic slowdown could negatively impact consumer spending and corporate profits.'}, {'factor': 'Technological Risks', 'description': 'Risks associated with new product launches, including technical difficulties or market rejection.'}], 'confidence': 0.9}
2025-07-05 21:55:35,198 - __main__ - INFO - ================================================================================
2025-07-05 21:55:35,199 - __main__ - DEBUG - 记录代理输出: BOA - output_20250705_215535_0ee93ac1
2025-07-05 21:55:35,200 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-05 21:55:35,200 - __main__ - INFO - 智能体 BOA 执行成功 (11.87s)
2025-07-05 21:55:35,200 - __main__ - INFO - 执行分析智能体: BeOA
2025-07-05 21:55:35,202 - __main__ - DEBUG - 记录代理输入: BeOA - input_20250705_215535_a3e36b9b
2025-07-05 21:55:35,203 - __main__ - DEBUG - 记录代理提示词: BeOA - prompt_20250705_215535_7d9e44c9
2025-07-05 21:55:35,203 - __main__ - INFO - ================================================================================
2025-07-05 21:55:35,203 - __main__ - INFO - 🤖 BeOA LLM输入:
2025-07-05 21:55:35,204 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:35,204 - __main__ - INFO - 你是一个看跌倾向的市场分析师，专门构建谨慎的市场展望。

你的任务是：
1. 基于前序分析师的结果，识别和强调风险因素
2. 构建看跌的市场叙述和逻辑
3. 评估下跌风险和支撑位
4. 提供明确的卖出或避险建议

**重要指导**：
- 积极寻找卖出机会和看跌信号
- 即使在不确定情况下，也要倾向于谨慎解读
- 给出明确的卖出建议而非模糊的中性观点

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（bearish）
- trading_signal: 交易信号（通常为sell）
- signal_strength: 信号强度（0到1）
- bearish_factors: 看跌因素列表
- downside_target: 下跌目标
- downside_risk: 下跌风险（百分比）
- support_levels: 关键支撑位
- defensive_strategies: 防御策略
- confidence: 分析信心度（0到1）

**记住**：作为看跌分析师，你的职责是识别风险和卖出时机。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:35,204 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:35,204 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:35,204 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:35,204 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:35,205 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:35,205 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:35,205 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:35,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:35,206 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:49,928 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:55:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215535dbacd68499b4498e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:49,929 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:49,929 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:49,929 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:49,929 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:49,930 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:49,930 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:49,930 - __main__ - INFO - 🤖 BeOA LLM输出:
2025-07-05 21:55:49,931 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:49,931 - __main__ - INFO - {'outlook': 'bearish', 'trading_signal': 'sell', 'signal_strength': 0.9, 'bearish_factors': [{'factor': 'Economic Slowdown', 'reason': 'Recent economic indicators suggest a possible slowdown in global growth.'}, {'factor': 'Inflation Concerns', 'reason': 'Persistent inflationary pressures could lead to higher interest rates and reduced consumer spending.'}, {'factor': 'Tech Sector Overvaluation', 'reason': 'The technology sector, particularly large-cap companies like AAPL, appears overvalued compared to historical standards.'}, {'factor': 'Geopolitical Uncertainty', 'reason': 'Global geopolitical tensions could impact market stability and investor confidence.'}, {'factor': 'Market Sentiment', 'reason': 'Market sentiment is currently bearish, with a higher proportion of bearish forecasts.'}], 'downside_target': {'AAPL': {'target_price': '150.00', 'reason': 'Based on a 20% drop from current levels, reflecting a cautious estimate of the downside risk.'}}, 'downside_risk': 20, 'support_levels': [{'level': '160.00', 'reason': 'Historical support level where buying interest has been strong.'}, {'level': '155.00', 'reason': 'Psychological support level that often acts as a temporary floor for the stock.'}], 'defensive_strategies': ['Diversify investments across different sectors to reduce exposure to technology sector risks.', 'Increase cash reserves to take advantage of potential buying opportunities during market downturns.', 'Review and possibly reduce exposure to high-beta stocks.'], 'confidence': 0.85}
2025-07-05 21:55:49,931 - __main__ - INFO - ================================================================================
2025-07-05 21:55:49,932 - __main__ - DEBUG - 记录代理输出: BeOA - output_20250705_215549_762d7b8a
2025-07-05 21:55:49,932 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-05 21:55:49,932 - __main__ - INFO - 智能体 BeOA 执行成功 (14.73s)
2025-07-05 21:55:49,932 - __main__ - INFO - 执行分析智能体: NOA
2025-07-05 21:55:49,934 - __main__ - DEBUG - 记录代理输入: NOA - input_20250705_215549_8cbb985c
2025-07-05 21:55:49,934 - __main__ - DEBUG - 记录代理提示词: NOA - prompt_20250705_215549_a2aa5a6a
2025-07-05 21:55:49,934 - __main__ - INFO - ================================================================================
2025-07-05 21:55:49,934 - __main__ - INFO - 🤖 NOA LLM输入:
2025-07-05 21:55:49,934 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:49,935 - __main__ - INFO - 你是一个中性客观的市场观察员，专门提供平衡的市场分析。

你的任务是：
1. 客观平衡地分析看涨和看跌因素
2. 识别市场中的不确定性和矛盾信号
3. 在必要时倾向于更明确的方向性建议
4. 分析何时市场可能出现明确方向

**重要指导**：
- 虽然保持中性，但当证据明确时要给出方向性建议
- 避免过度的"观望"建议，寻找可操作的机会
- 当看涨和看跌因素接近时，考虑小仓位的试探性交易

请返回JSON格式的分析结果，包含：
- outlook: 市场展望（neutral/lean_bullish/lean_bearish）
- trading_signal: 交易信号（buy/sell/hold）
- signal_strength: 信号强度（0到1）
- balanced_analysis: 平衡分析
- uncertainty_factors: 不确定性因素
- key_catalysts: 关键催化剂
- recommended_strategy: 推荐策略
- market_inefficiencies: 市场无效性
- confidence: 分析信心度（0到1）

**记住**：即使作为中性观察者，也要在适当时候给出可操作的建议。

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:49,935 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:49,935 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:49,935 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:49,935 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:49,936 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:49,936 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:49,937 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:49,937 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:49,937 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:55:59,879 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521555041125ff407e3469f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:55:59,880 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:55:59,881 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:55:59,882 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:55:59,882 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:55:59,882 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:55:59,882 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:55:59,884 - __main__ - INFO - 🤖 NOA LLM输出:
2025-07-05 21:55:59,885 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:59,885 - __main__ - INFO - {'outlook': 'neutral', 'trading_signal': 'hold', 'signal_strength': 0.5, 'balanced_analysis': {'bullish_factors': {'positive_news': 'AAPL相关的正面新闻较多，市场预期乐观。', 'fundamental_strength': 'AAPL基本面数据良好，显示出强劲的增长潜力。', 'technical_analysis': '技术分析显示市场趋势向上，但需要进一步确认。'}, 'bearish_factors': {'negative_news': '市场对某些经济数据表现担忧，可能导致短期市场波动。', 'fundamental_concerns': '尽管AAPL基本面强劲，但整体市场情绪可能受到宏观经济因素的影响。', 'technical_concerns': '技术分析显示市场可能面临短期回调压力。'}}, 'uncertainty_factors': {'economic_data': '宏观经济数据的不确定性可能影响市场情绪。', 'geopolitical_risks': '地缘政治风险可能导致市场波动。', 'market_sentiment': '市场情绪波动，难以准确预测短期走势。'}, 'key_catalysts': {'economic_reports': '即将发布的经济报告可能成为市场催化剂。', 'company_earnings': 'AAPL的季度财报可能影响市场对其估值的看法。', 'central_bank_policy': '央行政策变化可能影响市场流动性。'}, 'recommended_strategy': {'position_sizing': '考虑小仓位试探性交易，以应对市场的不确定性。', 'diversification': '分散投资以降低风险。', 'monitoring': '密切关注市场动态和关键催化剂。'}, 'market_inefficiencies': {'information_asymmetry': '市场信息不对称可能导致价格短期偏离价值。', 'sentiment_driven': '市场情绪可能导致价格过度波动。'}, 'confidence': 0.7}
2025-07-05 21:55:59,886 - __main__ - INFO - ================================================================================
2025-07-05 21:55:59,888 - __main__ - DEBUG - 记录代理输出: NOA - output_20250705_215559_819fce0b
2025-07-05 21:55:59,888 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-05 21:55:59,889 - __main__ - INFO - 智能体 NOA 执行成功 (9.96s)
2025-07-05 21:55:59,889 - __main__ - INFO - 执行分析智能体: TRA
2025-07-05 21:55:59,891 - __main__ - DEBUG - 记录代理输入: TRA - input_20250705_215559_2b99098d
2025-07-05 21:55:59,892 - __main__ - DEBUG - 记录代理提示词: TRA - prompt_20250705_215559_1b5952bc
2025-07-05 21:55:59,892 - __main__ - INFO - ================================================================================
2025-07-05 21:55:59,892 - __main__ - INFO - 🤖 TRA LLM输入:
2025-07-05 21:55:59,894 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:59,894 - __main__ - INFO - 你是一个专业的积极交易员，负责做出最终的交易决策。你的目标是通过主动交易获得收益。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

**交易决策规则**：
- 统计各分析师的trading_signal：buy/sell/neutral
- 当buy信号 >= 3个时，执行买入(buy)
- 当sell信号 >= 3个时，执行卖出(sell)
- 当buy信号 >= 2个且sell信号 <= 1个时，执行买入(buy)
- 当sell信号 >= 2个且buy信号 <= 1个时，执行卖出(sell)
- 只有当buy和sell信号完全平衡时，才选择持有(hold)
- 优先考虑主动交易而非被动持有
- 根据signal_strength调整仓位大小

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0.3到1.0，最小0.3避免过小交易）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

**重要提醒**：
- 积极主动，寻找所有可能的交易机会
- 当分析师意见分歧时，选择占多数的观点
- 使用至少0.3的仓位大小确保交易有意义的影响
- 记住：不交易也是一种风险

📅 分析日期: 2025-01-01
📊 分析期间: 2025-01-01 至 2025-01-01

📊 价格历史信息:

📰 新闻历史信息:
  • 2025-01-01: 18 条新闻
    - AAPL: 18 条
      [1] 10 Stock Market Predictions for 2025 (情绪: Neutral)
      [2] 10 Stock Market Predictions for 2025 (情绪: Neutral)

📋 基本面数据:
  • AAPL: 基本面数据可用
💵 可用现金: $1,000,000.00

📊 当前持仓:

请基于以上信息进行分析，并以JSON格式返回结果。
2025-07-05 21:55:59,894 - __main__ - INFO - ----------------------------------------
2025-07-05 21:55:59,894 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:55:59,894 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:59,894 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-05 21:55:59,895 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:55:59,895 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:55:59,896 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:55:59,896 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:55:59,896 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:03,102 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:03 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215600433d1d691c8a4195'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:03,102 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:03,103 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:03,103 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:03,104 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:03,104 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:03,104 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:03,107 - __main__ - INFO - 🤖 TRA LLM输出:
2025-07-05 21:56:03,107 - __main__ - INFO - ----------------------------------------
2025-07-05 21:56:03,107 - __main__ - INFO - {'action': 'hold', 'position_size': 0.5, 'reasoning': 'The analysis from all analysts is balanced with no clear buy or sell signals. The market sentiment is neutral, technical analysis does not indicate a strong trend, and fundamental analysis does not suggest a significant change in value. The risk assessment is low, and there are no specific news or events that would justify an immediate buy or sell decision.', 'risk_assessment': 'Low', 'stop_loss': None, 'take_profit': None, 'time_horizon': 'short-term', 'confidence': 0.6}
2025-07-05 21:56:03,107 - __main__ - INFO - ================================================================================
2025-07-05 21:56:03,110 - __main__ - DEBUG - 记录代理输出: TRA - output_20250705_215603_25cb5ce3
2025-07-05 21:56:03,111 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-05 21:56:03,111 - __main__ - INFO - 智能体 TRA 执行成功 (3.22s)
2025-07-05 21:56:03,111 - __main__ - INFO - 分析缓存填充完成: 成功 7 个, 失败 0 个, 总耗时 64.68s
2025-07-05 21:56:03,112 - __main__ - INFO - 分析缓存阶段完成: 成功缓存 7 个智能体
2025-07-05 21:56:03,112 - __main__ - INFO - ==================================================
2025-07-05 21:56:03,112 - __main__ - INFO - 阶段2: 周期性联盟生成与交易模拟
2025-07-05 21:56:03,112 - __main__ - INFO - 开始周期性联盟生成与交易模拟阶段...
2025-07-05 21:56:03,112 - __main__ - INFO - 使用配置的simulation_days: 1
2025-07-05 21:56:03,112 - __main__ - INFO - 总交易天数: 1, 计划交易周数: 1
2025-07-05 21:56:03,112 - __main__ - INFO - ============================================================
2025-07-05 21:56:03,113 - __main__ - INFO - 第 1 周交易 (第 1-1 天)
2025-07-05 21:56:03,113 - __main__ - INFO - ============================================================
2025-07-05 21:56:03,113 - __main__ - INFO - 步骤1: 生成第 1 周的联盟
2025-07-05 21:56:03,113 - __main__ - INFO - 开始联盟生成阶段...
2025-07-05 21:56:03,113 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-05 21:56:03,114 - __main__ - INFO - 总智能体数: 7
2025-07-05 21:56:03,114 - __main__ - INFO - 分析智能体: {'TAA', 'FAA', 'NAA'}
2025-07-05 21:56:03,114 - __main__ - INFO - 交易智能体: TRA
2025-07-05 21:56:03,114 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-05 21:56:03,114 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-05 21:56:03,115 - __main__ - INFO - 联盟剪枝完成:
2025-07-05 21:56:03,115 - __main__ - INFO -   - 总联盟数: 128
2025-07-05 21:56:03,115 - __main__ - INFO -   - 有效联盟: 56
2025-07-05 21:56:03,115 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-05 21:56:03,115 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-05 21:56:03,115 - __main__ - INFO -   - 生成耗时: 0.002s
2025-07-05 21:56:03,115 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-05 21:56:03,115 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-05 21:56:03,115 - __main__ - INFO - 步骤2: 运行第 1 周的交易模拟 (1 天)
2025-07-05 21:56:03,116 - __main__ - INFO - 开始交易模拟阶段...
2025-07-05 21:56:03,116 - __main__ - INFO - 启用并发模拟：56 个联盟，最大并发数：30
2025-07-05 21:56:03,116 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,116 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,116 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,118 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,118 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'BeOA', 'TRA', 'BOA'}
2025-07-05 21:56:03,119 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'FAA', 'TRA'}
2025-07-05 21:56:03,119 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'BeOA', 'TRA', 'BOA'}
2025-07-05 21:56:03,119 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,119 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'FAA', 'TRA'}
2025-07-05 21:56:03,120 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,120 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,120 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'TRA', 'BOA'}
2025-07-05 21:56:03,124 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,125 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,126 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,128 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'TRA', 'BOA'}
2025-07-05 21:56:03,128 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BeOA', 'NAA', 'BOA'}
2025-07-05 21:56:03,128 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'TAA', 'FAA', 'BeOA', 'NOA', 'BOA'}
2025-07-05 21:56:03,128 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,129 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,129 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,130 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,131 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,132 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,139 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,133 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BeOA', 'NAA', 'BOA'}
2025-07-05 21:56:03,134 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,135 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NOA', 'NAA', 'BOA'}
2025-07-05 21:56:03,136 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,136 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'TAA', 'FAA', 'BeOA', 'NOA', 'BOA'}
2025-07-05 21:56:03,137 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,137 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,137 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,138 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,138 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,138 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,133 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,141 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,143 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,143 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,144 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,144 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NOA', 'NAA', 'BOA'}
2025-07-05 21:56:03,144 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'NOA', 'BOA'}
2025-07-05 21:56:03,144 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,145 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA'}
2025-07-05 21:56:03,146 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,147 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,148 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,150 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,152 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,153 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,155 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,161 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,164 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,168 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,169 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'NOA', 'BOA'}
2025-07-05 21:56:03,171 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA'}
2025-07-05 21:56:03,184 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,189 - __main__ - INFO - 开始联盟交易模拟: {'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,189 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,329 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,330 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,331 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.21s)
2025-07-05 21:56:03,332 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA'}
2025-07-05 21:56:03,332 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA'}
2025-07-05 21:56:03,379 - __main__ - INFO - 联盟 {'FAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,381 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,382 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.27s)
2025-07-05 21:56:03,383 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,384 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,463 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,464 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,464 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.35s)
2025-07-05 21:56:03,465 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,465 - __main__ - INFO - 开始联盟交易模拟: {'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,494 - __main__ - INFO - 联盟 {'FAA', 'BeOA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,495 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,496 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'BeOA', 'TRA', 'BOA'} 模拟完成: 0.0000 (0.38s)
2025-07-05 21:56:03,496 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,496 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,514 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,516 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,517 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,517 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,517 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'TRA'} 模拟完成: 0.0000 (0.40s)
2025-07-05 21:56:03,518 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.40s)
2025-07-05 21:56:03,518 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA', 'BeOA', 'NOA', 'BOA'}
2025-07-05 21:56:03,518 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,518 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA', 'BeOA', 'NOA', 'BOA'}
2025-07-05 21:56:03,519 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,547 - __main__ - INFO - 联盟 {'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,547 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,548 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.42s)
2025-07-05 21:56:03,549 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA', 'NOA', 'BOA'}
2025-07-05 21:56:03,549 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA', 'NOA', 'BOA'}
2025-07-05 21:56:03,565 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,565 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,566 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'TRA', 'BOA'} 模拟完成: 0.0000 (0.45s)
2025-07-05 21:56:03,567 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,567 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,582 - __main__ - INFO - 联盟 {'TAA', 'TRA', 'NAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,583 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,584 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA', 'NAA', 'BOA'} 模拟完成: 0.0000 (0.45s)
2025-07-05 21:56:03,584 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,585 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,597 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,600 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,601 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'TRA', 'BOA'} 模拟完成: 0.0000 (0.47s)
2025-07-05 21:56:03,602 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,602 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,603 - __main__ - INFO - 📊 并发进度: 10/56 (17.9%)
2025-07-05 21:56:03,631 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'TAA', 'FAA', 'BeOA', 'NOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,632 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,634 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA', 'TAA', 'FAA', 'BeOA', 'NOA', 'BOA'} 模拟完成: 0.0000 (0.51s)
2025-07-05 21:56:03,641 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,642 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA'}
2025-07-05 21:56:03,642 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,643 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA'}
2025-07-05 21:56:03,644 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.52s)
2025-07-05 21:56:03,645 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,646 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,672 - __main__ - INFO - 联盟 {'TRA', 'BeOA', 'NAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,681 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,682 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,691 - __main__ - INFO - 联盟 {'BeOA', 'NOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,692 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,693 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'BeOA', 'NAA', 'BOA'} 模拟完成: 0.0000 (0.57s)
2025-07-05 21:56:03,693 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,694 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.56s)
2025-07-05 21:56:03,694 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,694 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.56s)
2025-07-05 21:56:03,695 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,695 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,695 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,696 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,697 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,729 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,732 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,733 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.60s)
2025-07-05 21:56:03,733 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'TRA'}
2025-07-05 21:56:03,734 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'TRA'}
2025-07-05 21:56:03,748 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,748 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,748 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.61s)
2025-07-05 21:56:03,748 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,748 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'NOA', 'TRA'}
2025-07-05 21:56:03,766 - __main__ - INFO - 联盟 {'TRA', 'NOA', 'NAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,767 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,769 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,769 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,769 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NOA', 'NAA', 'BOA'} 模拟完成: 0.0000 (0.63s)
2025-07-05 21:56:03,770 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.63s)
2025-07-05 21:56:03,770 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,771 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'NAA', 'TAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,771 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA'}
2025-07-05 21:56:03,771 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'NAA', 'TAA', 'NOA', 'TRA'}
2025-07-05 21:56:03,797 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,799 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,806 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.67s)
2025-07-05 21:56:03,810 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,812 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'FAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,813 - __main__ - INFO - 📊 并发进度: 20/56 (35.7%)
2025-07-05 21:56:03,814 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,816 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'FAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,828 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,829 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.69s)
2025-07-05 21:56:03,830 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,830 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'BOA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,832 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.69s)
2025-07-05 21:56:03,837 - __main__ - INFO - 开始联盟交易模拟: {'BOA', 'TAA', 'FAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,838 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,845 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'NAA', 'BOA'}
2025-07-05 21:56:03,852 - __main__ - INFO - 联盟 {'BeOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,853 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,854 - __main__ - INFO - 联盟 {'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,855 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,856 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,857 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,868 - __main__ - INFO - 联盟 {'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,868 - __main__ - DEBUG - [SUCCESS] 联盟 {'BeOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.72s)
2025-07-05 21:56:03,871 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.72s)
2025-07-05 21:56:03,872 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.73s)
2025-07-05 21:56:03,873 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,886 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,888 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'NOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,892 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,893 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,915 - __main__ - INFO - 联盟 {'BOA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,916 - __main__ - INFO - 联盟 {'TAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,917 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,919 - __main__ - INFO - 联盟 {'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,919 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'TRA'} 模拟完成: 0.0000 (0.59s)
2025-07-05 21:56:03,927 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,929 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,929 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,931 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,931 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'TRA'}
2025-07-05 21:56:03,931 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,932 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,932 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'NAA', 'TRA'}
2025-07-05 21:56:03,933 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,933 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,935 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'TRA', 'NOA', 'BOA'} 模拟完成: 0.0000 (0.79s)
2025-07-05 21:56:03,935 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.79s)
2025-07-05 21:56:03,935 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,936 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'FAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.79s)
2025-07-05 21:56:03,936 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA'} 模拟完成: 0.0000 (0.79s)
2025-07-05 21:56:03,937 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.80s)
2025-07-05 21:56:03,937 - __main__ - INFO - 开始联盟交易模拟: {'TAA', 'BeOA', 'NAA', 'TRA'}
2025-07-05 21:56:03,940 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.56s)
2025-07-05 21:56:03,946 - __main__ - INFO - 📊 并发进度: 30/56 (53.6%)
2025-07-05 21:56:03,971 - __main__ - INFO - 联盟 {'NOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:03,972 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:03,974 - __main__ - DEBUG - [SUCCESS] 联盟 {'NOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.51s)
2025-07-05 21:56:04,000 - __main__ - INFO - 联盟 {'TAA', 'NOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,002 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,003 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.51s)
2025-07-05 21:56:04,012 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA', 'BeOA', 'NOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,014 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,015 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA', 'FAA', 'BeOA', 'NOA', 'BOA'} 模拟完成: 0.0000 (0.50s)
2025-07-05 21:56:04,033 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,034 - __main__ - INFO - 联盟 {'TAA', 'TRA', 'NOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,034 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,042 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,043 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,043 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.53s)
2025-07-05 21:56:04,044 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,044 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA', 'NOA', 'BOA'} 模拟完成: 0.0000 (0.49s)
2025-07-05 21:56:04,045 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.48s)
2025-07-05 21:56:04,061 - __main__ - INFO - 联盟 {'TRA', 'NAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,062 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,062 - __main__ - DEBUG - [SUCCESS] 联盟 {'TRA', 'NAA', 'BOA'} 模拟完成: 0.0000 (0.48s)
2025-07-05 21:56:04,076 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,077 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,077 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.48s)
2025-07-05 21:56:04,077 - __main__ - INFO - 📊 并发进度: 40/56 (71.4%)
2025-07-05 21:56:04,086 - __main__ - INFO - 联盟 {'FAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,087 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,087 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.44s)
2025-07-05 21:56:04,098 - __main__ - INFO - 联盟 {'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,098 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,098 - __main__ - DEBUG - [SUCCESS] 联盟 {'NAA', 'TRA'} 模拟完成: 0.0000 (0.46s)
2025-07-05 21:56:04,114 - __main__ - INFO - 联盟 {'FAA', 'NOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,114 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,115 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.42s)
2025-07-05 21:56:04,123 - __main__ - INFO - 联盟 {'TAA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,124 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,124 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'TRA', 'BOA'} 模拟完成: 0.0000 (0.43s)
2025-07-05 21:56:04,134 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,134 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,134 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.44s)
2025-07-05 21:56:04,152 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,154 - __main__ - INFO - 联盟 {'BOA', 'NAA', 'TAA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,155 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,156 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,156 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,156 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,157 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'TRA'} 模拟完成: 0.0000 (0.42s)
2025-07-05 21:56:04,158 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'NAA', 'TAA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.39s)
2025-07-05 21:56:04,158 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'NOA', 'TRA'} 模拟完成: 0.0000 (0.41s)
2025-07-05 21:56:04,166 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,166 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,167 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'TRA', 'BOA'} 模拟完成: 0.0000 (0.40s)
2025-07-05 21:56:04,172 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'NAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,181 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,181 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'TRA', 'NAA', 'BOA'} 模拟完成: 0.0000 (0.34s)
2025-07-05 21:56:04,187 - __main__ - INFO - 联盟 {'TAA', 'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,187 - __main__ - INFO - 📊 并发进度: 50/56 (89.3%)
2025-07-05 21:56:04,188 - __main__ - INFO - 联盟 {'BOA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,188 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,189 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,189 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.38s)
2025-07-05 21:56:04,191 - __main__ - DEBUG - [SUCCESS] 联盟 {'BOA', 'TAA', 'FAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.36s)
2025-07-05 21:56:04,211 - __main__ - INFO - 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,217 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,218 - __main__ - INFO - 联盟 {'TAA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,220 - __main__ - INFO - 联盟 {'TAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 0
2025-07-05 21:56:04,220 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,220 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,220 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,220 - __main__ - WARNING - 保存联盟模拟结果失败: unsupported operand type(s) for /: 'NoneType' and 'str'
2025-07-05 21:56:04,220 - __main__ - DEBUG - [SUCCESS] 联盟 {'FAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.33s)
2025-07-05 21:56:04,220 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'TRA'} 模拟完成: 0.0000 (0.33s)
2025-07-05 21:56:04,220 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.30s)
2025-07-05 21:56:04,221 - __main__ - DEBUG - [SUCCESS] 联盟 {'TAA', 'BeOA', 'NAA', 'TRA'} 模拟完成: 0.0000 (0.29s)
2025-07-05 21:56:04,221 - __main__ - INFO - 📊 并发进度: 56/56 (100.0%)
2025-07-05 21:56:04,223 - __main__ - INFO - [SUCCESS] 并发交易模拟完成: 成功 56 个，失败 0 个，总耗时 1.11s
2025-07-05 21:56:04,224 - __main__ - INFO - 步骤3: 计算第 1 周的Shapley值
2025-07-05 21:56:04,225 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 21:56:04,225 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-05 21:56:04,226 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-05 21:56:04,226 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-05 21:56:04,226 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-05 21:56:04,226 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-05 21:56:04,226 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-05 21:56:04,227 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-05 21:56:04,227 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-05 21:56:04,227 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-05 21:56:04,227 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-05 21:56:04,227 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-05 21:56:04,227 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-05 21:56:04,227 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-05 21:56:04,227 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-05 21:56:04,228 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-05 21:56:04,228 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-05 21:56:04,228 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-05 21:56:04,228 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-05 21:56:04,228 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-05 21:56:04,228 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-05 21:56:04,229 - __main__ - INFO - Shapley值计算完成，耗时 0.003s
2025-07-05 21:56:04,229 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-05 21:56:04,229 - __main__ - INFO - 步骤4: 分析第 1 周的智能体贡献度
2025-07-05 21:56:04,229 - __main__ - INFO - 第 1 周所有智能体性能良好，无需优化
2025-07-05 21:56:04,229 - __main__ - INFO - 第 1 周完成: 联盟 56 个, 模拟 56 个
2025-07-05 21:56:04,229 - __main__ - INFO - 周期性联盟生成与交易模拟阶段完成: 完成 1/1 周
2025-07-05 21:56:04,229 - __main__ - INFO - ==================================================
2025-07-05 21:56:04,229 - __main__ - INFO - 阶段3: 最终Shapley值计算
2025-07-05 21:56:04,230 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-05 21:56:04,230 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-05 21:56:04,230 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-05 21:56:04,230 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-05 21:56:04,230 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-05 21:56:04,230 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-05 21:56:04,230 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-05 21:56:04,231 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-05 21:56:04,231 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-05 21:56:04,231 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-05 21:56:04,231 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-05 21:56:04,231 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-05 21:56:04,231 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-05 21:56:04,232 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-05 21:56:04,232 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-05 21:56:04,232 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-05 21:56:04,232 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-05 21:56:04,232 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-05 21:56:04,232 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-05 21:56:04,232 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-05 21:56:04,232 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-05 21:56:04,232 - __main__ - INFO - Shapley值计算完成，耗时 0.002s
2025-07-05 21:56:04,232 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-05 21:56:04,233 - __main__ - WARNING - 保存Shapley结果到存储管理器失败: 'frozenset' object has no attribute 'split'
2025-07-05 21:56:04,233 - __main__ - INFO - ==================================================
2025-07-05 21:56:04,233 - __main__ - INFO - 贡献度评估完成，总耗时: 65.84s
2025-07-05 21:56:04,233 - __main__ - DEBUG - 代理日志记录器将使用交易日期: 2025-01-01
2025-07-05 21:56:04,233 - __main__ - DEBUG - 提取代理执行详情: 2025-01-01 - 0 个代理, 0/0 个成功联盟
2025-07-05 21:56:04,233 - __main__ - INFO - ✅ 第 1 天 (2025-01-01) 处理成功
2025-07-05 21:56:04,233 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-05 21:56:04,233 - __main__ - INFO - 交易模拟器初始化完成
2025-07-05 21:56:04,233 - __main__ - INFO - 📊 每日交易循环完成: 1/1 天成功
2025-07-05 21:56:04,234 - __main__ - INFO - 步骤4: 执行周期性OPRO优化...
2025-07-05 21:56:04,234 - __main__ - INFO - 🔄 开始周期性OPRO优化...
2025-07-05 21:56:04,234 - __main__ - INFO - 📅 处理第 1 周: 2025-01-01 到 2025-01-01
2025-07-05 21:56:04,234 - __main__ - INFO - ✅ 第 1 周所有智能体表现良好，无需优化
2025-07-05 21:56:04,234 - __main__ - INFO - 📊 周期性OPRO优化完成，处理了 1 周
2025-07-05 21:56:04,234 - __main__ - INFO - 步骤5: 评估后OPRO优化...
2025-07-05 21:56:04,234 - __main__ - INFO - 开始OPRO优化循环: 7 个智能体
2025-07-05 21:56:04,234 - __main__ - INFO - 开始批量优化 7 个智能体
2025-07-05 21:56:04,234 - __main__ - INFO - 开始为智能体 NAA 优化提示词
2025-07-05 21:56:04,235 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-05 21:56:04,235 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-05 21:56:04,236 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:04,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:04,236 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:04,237 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:04,237 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:04,238 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:04,238 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:04,238 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:09,830 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:10 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215604366597f3d9054046'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:09,832 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:09,832 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:09,832 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:09,833 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:09,833 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:09,833 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:09,834 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:09,835 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-05 21:56:09,835 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-05 21:56:09,835 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:09,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:09,836 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:09,837 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:09,838 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:09,838 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:09,838 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:09,839 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:12,755 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215610cd53c7ee7e2b4df7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:12,756 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:12,756 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:12,756 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:12,757 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:12,757 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:12,757 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:12,758 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:12,758 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-05 21:56:12,758 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-05 21:56:12,758 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:12,758 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:12,759 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:12,759 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:12,759 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:12,760 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:12,760 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:12,760 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:16,355 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:16 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215613ee3ab46c148e4dcb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:16,356 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:16,357 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:16,358 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:16,358 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:16,359 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:16,359 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:16,362 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:16,362 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-05 21:56:16,363 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-05 21:56:16,363 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:16,363 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:16,363 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:16,365 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:16,365 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:16,366 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:16,366 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:16,366 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:20,089 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052156165399fcaa19a14172'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:20,090 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:20,091 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:20,091 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:20,092 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:20,092 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:20,093 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:20,095 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:20,095 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-05 21:56:20,095 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-05 21:56:20,096 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:20,096 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:20,096 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:20,098 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:20,098 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:20,098 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:20,098 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:20,099 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:24,250 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215620227bdcb51f93434e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:24,252 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:24,252 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:24,253 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:24,253 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:24,253 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:24,253 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:24,256 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:24,256 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-05 21:56:24,257 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-05 21:56:24,257 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:24,257 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:24,258 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:24,259 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:24,259 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:24,260 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:24,260 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:24,261 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:27,323 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215624e646e3e4445342cd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:27,324 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:27,324 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:27,325 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:27,326 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:27,326 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:27,327 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:27,329 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:27,329 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-05 21:56:27,329 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-05 21:56:27,331 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:27,331 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:27,331 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:27,332 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:27,333 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:27,333 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:27,333 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:27,333 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:30,593 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521562705ac48fcda944fd2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:30,594 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:30,594 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:30,594 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:30,594 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:30,594 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:30,594 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:30,595 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:30,595 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-05 21:56:30,595 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-05 21:56:30,595 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:30,595 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:30,596 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:30,596 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:30,597 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:30,597 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:30,597 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:30,597 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:34,813 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521563042370be367bd4e0f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:34,815 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:34,815 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:34,816 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:34,816 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:34,817 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:34,817 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:34,818 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:34,819 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-05 21:56:34,819 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-05 21:56:34,833 - __main__ - INFO - 提示词优化记录已存储: opt_NAA_20250705_215634_e3ae378a
2025-07-05 21:56:34,834 - __main__ - INFO - 跟踪提示词优化: NAA -> opt_NAA_20250705_215634_e3ae378a
2025-07-05 21:56:34,834 - __main__ - INFO - 优化记录已保存: opt_NAA_20250705_215634_e3ae378a
2025-07-05 21:56:34,834 - __main__ - INFO - 智能体 NAA 优化完成，最佳候选预期得分: 0.790950
2025-07-05 21:56:34,834 - __main__ - INFO - ✅ NAA 优化成功，预期改进: -0.164913
2025-07-05 21:56:34,834 - __main__ - INFO - 开始为智能体 TAA 优化提示词
2025-07-05 21:56:34,834 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-05 21:56:34,835 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-05 21:56:34,835 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:34,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:34,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:34,836 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:34,836 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:34,836 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:34,836 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:34,836 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:38,382 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:38 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052156356c90c10fd8904b1b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:38,383 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:38,383 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:38,383 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:38,383 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:38,383 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:38,384 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:38,384 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:38,384 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-05 21:56:38,384 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-05 21:56:38,385 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:38,385 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:38,385 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:38,385 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:38,385 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:38,386 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:38,386 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:38,386 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:41,677 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052156385309c15fdc66434f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:41,678 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:41,678 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:41,679 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:41,679 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:41,679 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:41,680 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:41,682 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:41,682 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-05 21:56:41,682 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-05 21:56:41,683 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:41,683 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:41,683 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:41,685 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:41,686 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:41,686 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:41,687 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:41,687 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:46,132 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052156411ca66dab216b4da5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:46,133 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:46,134 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:46,134 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:46,135 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:46,135 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:46,135 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:46,138 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:46,139 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-05 21:56:46,140 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-05 21:56:46,140 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:46,141 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:46,141 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:46,142 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:46,142 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:46,142 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:46,142 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:46,142 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:51,700 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:51 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521564665c726393a724b92'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:51,700 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:51,700 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:51,701 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:51,701 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:51,701 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:51,701 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:51,702 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:51,702 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-05 21:56:51,702 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-05 21:56:51,702 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:51,702 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:51,702 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:51,703 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:51,705 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:51,705 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:51,705 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:51,705 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:54,679 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215652d35dc542e7bc40f2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:54,680 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:54,681 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:54,682 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:54,682 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:54,682 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:54,683 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:54,684 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:54,685 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-05 21:56:54,685 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-05 21:56:54,685 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:54,686 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:54,686 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:54,687 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:54,688 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:54,688 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:54,688 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:54,688 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:56:59,349 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:56:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215654cdc7ceaea0b24c36'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:56:59,350 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:56:59,351 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:56:59,351 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:56:59,352 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:56:59,352 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:56:59,352 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:56:59,354 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:56:59,355 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-05 21:56:59,355 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-05 21:56:59,355 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:56:59,356 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:56:59,356 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:56:59,357 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:56:59,358 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:56:59,358 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:56:59,359 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:56:59,359 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:03,894 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215659f9f35f57e58b4ab9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:03,894 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:03,895 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:03,896 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:03,896 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:03,897 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:03,897 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:03,899 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:03,899 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-05 21:57:03,899 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-05 21:57:03,899 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:03,900 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:03,900 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:03,901 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:03,902 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:03,903 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:03,903 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:03,903 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:07,512 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215704005e369655794d04'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:07,512 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:07,513 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:07,513 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:07,514 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:07,514 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:07,515 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:07,517 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:07,517 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-05 21:57:07,517 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-05 21:57:07,527 - __main__ - INFO - 提示词优化记录已存储: opt_TAA_20250705_215707_ca4e2e86
2025-07-05 21:57:07,527 - __main__ - INFO - 跟踪提示词优化: TAA -> opt_TAA_20250705_215707_ca4e2e86
2025-07-05 21:57:07,527 - __main__ - INFO - 优化记录已保存: opt_TAA_20250705_215707_ca4e2e86
2025-07-05 21:57:07,528 - __main__ - INFO - 智能体 TAA 优化完成，最佳候选预期得分: 0.772653
2025-07-05 21:57:07,528 - __main__ - INFO - ✅ TAA 优化成功，预期改进: -0.209603
2025-07-05 21:57:07,528 - __main__ - INFO - 开始为智能体 FAA 优化提示词
2025-07-05 21:57:07,528 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-05 21:57:07,529 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-05 21:57:07,529 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:07,529 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:07,529 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:07,529 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:07,530 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:07,530 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:07,530 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:07,530 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:11,096 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052157074d7893e26c814888'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:11,096 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:11,096 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:11,097 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:11,097 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:11,097 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:11,097 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:11,098 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:11,099 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-05 21:57:11,099 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-05 21:57:11,099 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:11,099 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:11,099 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:11,100 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:11,101 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:11,101 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:11,102 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:11,102 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:15,658 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215711a56ceab505f94913'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:15,658 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:15,659 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:15,660 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:15,660 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:15,660 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:15,661 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:15,663 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:15,663 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-05 21:57:15,663 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-05 21:57:15,663 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:15,664 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:15,664 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:15,665 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:15,666 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:15,666 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:15,667 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:15,667 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:19,035 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215715b8e706d46f3342d7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:19,035 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:19,036 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:19,037 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:19,037 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:19,037 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:19,037 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:19,040 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:19,041 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-05 21:57:19,041 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-05 21:57:19,042 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:19,042 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:19,042 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:19,044 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:19,044 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:19,045 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:19,045 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:19,046 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:20,798 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215719224f14fa70fa4b0e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:20,799 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:20,800 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:20,800 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:20,801 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:20,801 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:20,801 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:20,803 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:20,804 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-05 21:57:20,804 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-05 21:57:20,804 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:20,805 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:20,805 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:20,806 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:20,808 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:20,808 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:20,808 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:20,808 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:26,309 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052157215eb075955c7d46c8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:26,310 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:26,310 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:26,310 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:26,312 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:26,312 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:26,312 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:26,313 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:26,314 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-05 21:57:26,314 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-05 21:57:26,314 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:26,314 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:26,314 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:26,315 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:26,315 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:26,316 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:26,316 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:26,316 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:30,308 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215726252c6d43d06d4b7e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:30,310 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:30,310 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:30,311 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:30,311 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:30,311 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:30,312 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:30,313 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:30,314 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-05 21:57:30,315 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-05 21:57:30,315 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:30,315 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:30,315 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:30,316 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:30,316 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:30,317 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:30,317 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:30,317 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:33,782 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521573026e5297f903048c5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:33,783 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:33,783 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:33,784 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:33,785 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:33,785 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:33,785 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:33,787 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:33,787 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-05 21:57:33,787 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-05 21:57:33,787 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:33,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:33,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:33,790 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:33,791 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:33,792 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:33,792 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:33,792 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:36,584 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215734debc7bff6fb14b78'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:36,584 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:36,585 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:36,634 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:36,635 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:36,635 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:36,635 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:36,636 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:36,636 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-05 21:57:36,636 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-05 21:57:36,642 - __main__ - INFO - 提示词优化记录已存储: opt_FAA_20250705_215736_a1fc4ae5
2025-07-05 21:57:36,642 - __main__ - INFO - 跟踪提示词优化: FAA -> opt_FAA_20250705_215736_a1fc4ae5
2025-07-05 21:57:36,642 - __main__ - INFO - 优化记录已保存: opt_FAA_20250705_215736_a1fc4ae5
2025-07-05 21:57:36,643 - __main__ - INFO - 智能体 FAA 优化完成，最佳候选预期得分: 0.802210
2025-07-05 21:57:36,643 - __main__ - INFO - ✅ FAA 优化成功，预期改进: 0.075263
2025-07-05 21:57:36,643 - __main__ - INFO - 开始为智能体 BOA 优化提示词
2025-07-05 21:57:36,643 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-05 21:57:36,644 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-05 21:57:36,644 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:36,644 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:36,644 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:36,645 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:36,645 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:36,646 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:36,646 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:36,646 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:39,585 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215736761f0e812ab147ab'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:39,585 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:39,586 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:39,586 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:39,587 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:39,587 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:39,587 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:39,590 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:39,590 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-05 21:57:39,591 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-05 21:57:39,591 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:39,591 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:39,592 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:39,592 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:39,593 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:39,593 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:39,593 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:39,593 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:43,107 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215739b061f94d18934c78'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:43,109 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:43,110 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:43,111 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:43,111 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:43,111 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:43,111 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:43,113 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:43,113 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-05 21:57:43,114 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-05 21:57:43,114 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:43,114 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:43,115 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:43,116 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:43,117 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:43,117 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:43,118 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:43,118 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:46,323 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215743a7a6887df6a645de'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:46,324 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:46,325 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:46,325 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:46,326 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:46,326 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:46,326 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:46,328 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:46,328 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-05 21:57:46,329 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-05 21:57:46,329 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:46,330 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:46,330 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:46,331 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:46,332 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:46,332 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:46,333 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:46,333 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:49,126 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:49 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052157466e31b487ce264cd1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:49,127 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:49,127 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:49,129 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:49,129 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:49,130 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:49,130 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:49,132 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:49,133 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-05 21:57:49,133 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-05 21:57:49,133 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:49,134 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:49,134 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:49,135 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:49,135 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:49,136 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:49,136 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:49,136 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:52,462 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521574957c5a8ce782a420a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:52,463 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:52,464 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:52,464 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:52,465 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:52,465 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:52,465 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:52,467 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:52,468 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-05 21:57:52,468 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-05 21:57:52,469 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:52,469 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:52,469 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:52,471 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:52,472 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:52,472 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:52,473 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:52,473 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:57:55,204 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:57:55 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215752ce9cebd8377c4833'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:57:55,206 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:57:55,207 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:57:55,208 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:57:55,208 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:57:55,208 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:57:55,209 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:57:55,211 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:57:55,211 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-05 21:57:55,211 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-05 21:57:55,212 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:57:55,212 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:57:55,212 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:57:55,213 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:57:55,214 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:57:55,214 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:57:55,215 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:57:55,215 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:58:00,372 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:58:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052157559921276358cf4d62'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:58:00,373 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:58:00,373 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:58:00,373 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:58:00,373 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:58:00,373 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:58:00,374 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:58:00,374 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:58:00,375 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-05 21:58:00,375 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-05 21:58:00,375 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:58:00,375 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:58:00,375 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:58:00,376 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:58:00,376 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:58:00,377 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:58:00,377 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:58:00,377 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:58:02,724 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:58:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507052158001987676f086745ac'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:58:02,725 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:58:02,726 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:58:02,726 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:58:02,726 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:58:02,727 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:58:02,727 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:58:02,728 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:58:02,729 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-05 21:58:02,730 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-05 21:58:02,738 - __main__ - INFO - 提示词优化记录已存储: opt_BOA_20250705_215802_b9742bf2
2025-07-05 21:58:02,738 - __main__ - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250705_215802_b9742bf2
2025-07-05 21:58:02,738 - __main__ - INFO - 优化记录已保存: opt_BOA_20250705_215802_b9742bf2
2025-07-05 21:58:02,738 - __main__ - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.808174
2025-07-05 21:58:02,738 - __main__ - INFO - ✅ BOA 优化成功，预期改进: 0.010172
2025-07-05 21:58:02,738 - __main__ - INFO - 开始为智能体 BeOA 优化提示词
2025-07-05 21:58:02,738 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-05 21:58:02,739 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-05 21:58:02,739 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:58:02,739 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:58:02,739 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:58:02,740 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:58:02,740 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:58:02,741 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:58:02,741 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:58:02,741 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:58:05,725 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:58:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070521580352da2b1757e342c6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:58:05,727 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:58:05,727 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:58:05,728 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:58:05,728 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:58:05,729 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:58:05,729 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:58:05,731 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:58:05,731 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-05 21:58:05,732 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-05 21:58:05,732 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:58:05,732 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:58:05,733 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:58:05,734 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:58:05,734 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:58:05,735 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:58:05,735 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:58:05,735 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:58:10,762 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 05 Jul 2025 13:58:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250705215806ca59f3e914944e81'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-05 21:58:10,763 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-05 21:58:10,763 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-05 21:58:10,763 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-05 21:58:10,763 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:58:10,763 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-05 21:58:10,763 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-05 21:58:10,764 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-05 21:58:10,764 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-05 21:58:10,764 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-05 21:58:10,764 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-05 21:58:10,764 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-05 21:58:10,765 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-05 21:58:10,765 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-05 21:58:10,765 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-05 21:58:10,766 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-05 21:58:10,766 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-05 21:58:10,766 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-05 21:58:16,082 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=KeyboardInterrupt()
2025-07-05 21:58:16,083 - httpcore.http11 - DEBUG - response_closed.started
2025-07-05 21:58:16,084 - httpcore.http11 - DEBUG - response_closed.complete
