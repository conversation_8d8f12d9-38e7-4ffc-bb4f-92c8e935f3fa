{"experiment_id": "experiment_20250705_212617", "report_generation_time": "2025-07-05T21:26:17.815083", "coalition_summary": {"FAA_NAA": {"timestamp": "2025-07-05T21:26:17.794055", "sharpe_ratio": 1.01, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "FAA_NAA_TAA": {"timestamp": "2025-07-05T21:26:17.801554", "sharpe_ratio": 1.06, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "FAA_NAA_TRA": {"timestamp": "2025-07-05T21:26:17.803554", "sharpe_ratio": 1.08, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "FAA_TAA": {"timestamp": "2025-07-05T21:26:17.797045", "sharpe_ratio": 1.03, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "FAA_TAA_TRA": {"timestamp": "2025-07-05T21:26:17.805554", "sharpe_ratio": 1.09, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "FAA_TRA": {"timestamp": "2025-07-05T21:26:17.799548", "sharpe_ratio": 1.05, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "NAA_TAA": {"timestamp": "2025-07-05T21:26:17.792046", "sharpe_ratio": 1.0, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "NAA_TAA_TRA": {"timestamp": "2025-07-05T21:26:17.802554", "sharpe_ratio": 1.07, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "NAA_TRA": {"timestamp": "2025-07-05T21:26:17.795046", "sharpe_ratio": 1.02, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}, "TAA_TRA": {"timestamp": "2025-07-05T21:26:17.798045", "sharpe_ratio": 1.04, "total_return": 0.0, "volatility": 0.0, "max_drawdown": 0.0, "win_rate": 0.0, "total_trades": 0}}, "performance_analysis": {"best_coalition": {"name": "FAA_TAA_TRA", "sharpe_ratio": 1.09}, "worst_coalition": {"name": "NAA_TAA", "sharpe_ratio": 1.0}, "average_sharpe": 1.0450000000000002, "performance_distribution": {}, "insights": []}, "recommendations": []}